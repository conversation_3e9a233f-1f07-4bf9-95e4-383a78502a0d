[{"name": "generate-buildid", "duration": 269, "timestamp": 234347404512, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750436403699, "traceId": "48385770410509ac"}, {"name": "load-custom-routes", "duration": 437, "timestamp": 234347404915, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750436403699, "traceId": "48385770410509ac"}, {"name": "create-dist-dir", "duration": 6608, "timestamp": 234347529615, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750436403824, "traceId": "48385770410509ac"}, {"name": "create-pages-mapping", "duration": 276, "timestamp": 234347721177, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750436404016, "traceId": "48385770410509ac"}, {"name": "collect-app-paths", "duration": 1872, "timestamp": 234347721509, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750436404016, "traceId": "48385770410509ac"}, {"name": "create-app-mapping", "duration": 1078, "timestamp": 234347723422, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750436404018, "traceId": "48385770410509ac"}, {"name": "public-dir-conflict-check", "duration": 807, "timestamp": 234347725040, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750436404020, "traceId": "48385770410509ac"}, {"name": "generate-routes-manifest", "duration": 3550, "timestamp": 234347726165, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750436404021, "traceId": "48385770410509ac"}, {"name": "create-entrypoints", "duration": 21676, "timestamp": 234348825899, "id": 15, "parentId": 13, "tags": {}, "startTime": 1750436405126, "traceId": "48385770410509ac"}, {"name": "generate-webpack-config", "duration": 464101, "timestamp": 234348847754, "id": 16, "parentId": 14, "tags": {}, "startTime": 1750436405148, "traceId": "48385770410509ac"}, {"name": "next-trace-entrypoint-plugin", "duration": 5159, "timestamp": 234349447703, "id": 18, "parentId": 17, "tags": {}, "startTime": 1750436405748, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 237193, "timestamp": 234349460487, "id": 22, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1750436405761, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 266202, "timestamp": 234349460360, "id": 21, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1750436405761, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 315643, "timestamp": 234349460532, "id": 24, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1750436405761, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 333898, "timestamp": 234349459892, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Ccrm-staff%5Cinno-crm-staff%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750436405760, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 364619, "timestamp": 234349460512, "id": 23, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5Ccrm-staff%5Cinno-crm-staff%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1750436405761, "traceId": "48385770410509ac"}, {"name": "make", "duration": 505211, "timestamp": 234349459574, "id": 19, "parentId": 17, "tags": {}, "startTime": 1750436405760, "traceId": "48385770410509ac"}, {"name": "get-entries", "duration": 1093, "timestamp": 234349967964, "id": 34, "parentId": 33, "tags": {}, "startTime": 1750436406268, "traceId": "48385770410509ac"}, {"name": "node-file-trace-plugin", "duration": 97957, "timestamp": 234349972345, "id": 35, "parentId": 33, "tags": {"traceEntryCount": "6"}, "startTime": 1750436406273, "traceId": "48385770410509ac"}, {"name": "collect-traced-files", "duration": 585, "timestamp": 234350070321, "id": 36, "parentId": 33, "tags": {}, "startTime": 1750436406370, "traceId": "48385770410509ac"}, {"name": "finish-modules", "duration": 103190, "timestamp": 234349967723, "id": 33, "parentId": 18, "tags": {}, "startTime": 1750436406268, "traceId": "48385770410509ac"}, {"name": "chunk-graph", "duration": 9111, "timestamp": 234350096044, "id": 38, "parentId": 37, "tags": {}, "startTime": 1750436406396, "traceId": "48385770410509ac"}, {"name": "optimize-modules", "duration": 31, "timestamp": 234350105294, "id": 40, "parentId": 37, "tags": {}, "startTime": 1750436406405, "traceId": "48385770410509ac"}, {"name": "optimize-chunks", "duration": 12801, "timestamp": 234350105413, "id": 41, "parentId": 37, "tags": {}, "startTime": 1750436406406, "traceId": "48385770410509ac"}, {"name": "optimize-tree", "duration": 166, "timestamp": 234350118353, "id": 42, "parentId": 37, "tags": {}, "startTime": 1750436406419, "traceId": "48385770410509ac"}, {"name": "optimize-chunk-modules", "duration": 11928, "timestamp": 234350118622, "id": 43, "parentId": 37, "tags": {}, "startTime": 1750436406419, "traceId": "48385770410509ac"}, {"name": "optimize", "duration": 25490, "timestamp": 234350105235, "id": 39, "parentId": 37, "tags": {}, "startTime": 1750436406405, "traceId": "48385770410509ac"}, {"name": "module-hash", "duration": 23514, "timestamp": 234350162014, "id": 44, "parentId": 37, "tags": {}, "startTime": 1750436406462, "traceId": "48385770410509ac"}, {"name": "code-generation", "duration": 9927, "timestamp": 234350185692, "id": 45, "parentId": 37, "tags": {}, "startTime": 1750436406486, "traceId": "48385770410509ac"}, {"name": "hash", "duration": 11157, "timestamp": 234350205968, "id": 46, "parentId": 37, "tags": {}, "startTime": 1750436406506, "traceId": "48385770410509ac"}, {"name": "code-generation-jobs", "duration": 547, "timestamp": 234350217115, "id": 47, "parentId": 37, "tags": {}, "startTime": 1750436406517, "traceId": "48385770410509ac"}, {"name": "module-assets", "duration": 352, "timestamp": 234350217584, "id": 48, "parentId": 37, "tags": {}, "startTime": 1750436406518, "traceId": "48385770410509ac"}, {"name": "create-chunk-assets", "duration": 1432, "timestamp": 234350217953, "id": 49, "parentId": 37, "tags": {}, "startTime": 1750436406518, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 596, "timestamp": 234350230696, "id": 51, "parentId": 50, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 190, "timestamp": 234350231132, "id": 52, "parentId": 50, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 149, "timestamp": 234350231178, "id": 53, "parentId": 50, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 137, "timestamp": 234350231199, "id": 54, "parentId": 50, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 122, "timestamp": 234350231216, "id": 55, "parentId": 50, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 109, "timestamp": 234350231235, "id": 56, "parentId": 50, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 96, "timestamp": 234350231250, "id": 57, "parentId": 50, "tags": {"name": "825.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 76, "timestamp": 234350231271, "id": 58, "parentId": 50, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1750436406531, "traceId": "48385770410509ac"}, {"name": "minify-webpack-plugin-optimize", "duration": 6654, "timestamp": 234350225135, "id": 50, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1750436406525, "traceId": "48385770410509ac"}, {"name": "css-minimizer-plugin", "duration": 407, "timestamp": 234350232742, "id": 59, "parentId": 17, "tags": {}, "startTime": 1750436406533, "traceId": "48385770410509ac"}, {"name": "create-trace-assets", "duration": 1760, "timestamp": 234350233668, "id": 60, "parentId": 18, "tags": {}, "startTime": 1750436406534, "traceId": "48385770410509ac"}, {"name": "seal", "duration": 155612, "timestamp": 234350086349, "id": 37, "parentId": 17, "tags": {}, "startTime": 1750436406387, "traceId": "48385770410509ac"}, {"name": "webpack-compilation", "duration": 802489, "timestamp": 234349445737, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750436405746, "traceId": "48385770410509ac"}, {"name": "emit", "duration": 23583, "timestamp": 234350249722, "id": 61, "parentId": 14, "tags": {}, "startTime": 1750436406550, "traceId": "48385770410509ac"}, {"name": "webpack-close", "duration": 1631, "timestamp": 234350275683, "id": 62, "parentId": 14, "tags": {"name": "server"}, "startTime": 1750436406576, "traceId": "48385770410509ac"}, {"name": "webpack-generate-error-stats", "duration": 5744, "timestamp": 234350277407, "id": 63, "parentId": 62, "tags": {}, "startTime": 1750436406578, "traceId": "48385770410509ac"}, {"name": "run-webpack-compiler", "duration": 1457786, "timestamp": 234348825867, "id": 14, "parentId": 13, "tags": {}, "startTime": 1750436405126, "traceId": "48385770410509ac"}, {"name": "format-webpack-messages", "duration": 129, "timestamp": 234350283666, "id": 64, "parentId": 13, "tags": {}, "startTime": 1750436406584, "traceId": "48385770410509ac"}, {"name": "worker-main-server", "duration": 1458740, "timestamp": 234348825280, "id": 13, "parentId": 1, "tags": {}, "startTime": 1750436405125, "traceId": "48385770410509ac"}, {"name": "create-entrypoints", "duration": 22956, "timestamp": 234351355768, "id": 67, "parentId": 65, "tags": {}, "startTime": 1750436407647, "traceId": "48385770410509ac"}, {"name": "generate-webpack-config", "duration": 454096, "timestamp": 234351378929, "id": 68, "parentId": 66, "tags": {}, "startTime": 1750436407670, "traceId": "48385770410509ac"}, {"name": "make", "duration": 901, "timestamp": 234351977497, "id": 70, "parentId": 69, "tags": {}, "startTime": 1750436408269, "traceId": "48385770410509ac"}, {"name": "chunk-graph", "duration": 951, "timestamp": 234351982422, "id": 72, "parentId": 71, "tags": {}, "startTime": 1750436408273, "traceId": "48385770410509ac"}, {"name": "optimize-modules", "duration": 48, "timestamp": 234351983546, "id": 74, "parentId": 71, "tags": {}, "startTime": 1750436408275, "traceId": "48385770410509ac"}, {"name": "optimize-chunks", "duration": 1055, "timestamp": 234351983709, "id": 75, "parentId": 71, "tags": {}, "startTime": 1750436408275, "traceId": "48385770410509ac"}, {"name": "optimize-tree", "duration": 132, "timestamp": 234351984856, "id": 76, "parentId": 71, "tags": {}, "startTime": 1750436408276, "traceId": "48385770410509ac"}, {"name": "optimize-chunk-modules", "duration": 576, "timestamp": 234351985234, "id": 77, "parentId": 71, "tags": {}, "startTime": 1750436408276, "traceId": "48385770410509ac"}, {"name": "optimize", "duration": 2441, "timestamp": 234351983470, "id": 73, "parentId": 71, "tags": {}, "startTime": 1750436408274, "traceId": "48385770410509ac"}, {"name": "module-hash", "duration": 155, "timestamp": 234351987440, "id": 78, "parentId": 71, "tags": {}, "startTime": 1750436408278, "traceId": "48385770410509ac"}, {"name": "code-generation", "duration": 308, "timestamp": 234351987670, "id": 79, "parentId": 71, "tags": {}, "startTime": 1750436408279, "traceId": "48385770410509ac"}, {"name": "hash", "duration": 590, "timestamp": 234351988313, "id": 80, "parentId": 71, "tags": {}, "startTime": 1750436408279, "traceId": "48385770410509ac"}, {"name": "code-generation-jobs", "duration": 183, "timestamp": 234351988897, "id": 81, "parentId": 71, "tags": {}, "startTime": 1750436408280, "traceId": "48385770410509ac"}, {"name": "module-assets", "duration": 112, "timestamp": 234351989035, "id": 82, "parentId": 71, "tags": {}, "startTime": 1750436408280, "traceId": "48385770410509ac"}, {"name": "create-chunk-assets", "duration": 265, "timestamp": 234351989161, "id": 83, "parentId": 71, "tags": {}, "startTime": 1750436408280, "traceId": "48385770410509ac"}, {"name": "minify-js", "duration": 231, "timestamp": 234352005255, "id": 85, "parentId": 84, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1750436408296, "traceId": "48385770410509ac"}, {"name": "minify-webpack-plugin-optimize", "duration": 4123, "timestamp": 234352001379, "id": 84, "parentId": 69, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1750436408292, "traceId": "48385770410509ac"}, {"name": "css-minimizer-plugin", "duration": 154, "timestamp": 234352005624, "id": 86, "parentId": 69, "tags": {}, "startTime": 1750436408297, "traceId": "48385770410509ac"}, {"name": "seal", "duration": 29083, "timestamp": 234351980926, "id": 71, "parentId": 69, "tags": {}, "startTime": 1750436408272, "traceId": "48385770410509ac"}, {"name": "webpack-compilation", "duration": 42019, "timestamp": 234351968408, "id": 69, "parentId": 66, "tags": {"name": "edge-server"}, "startTime": 1750436408259, "traceId": "48385770410509ac"}, {"name": "emit", "duration": 5100, "timestamp": 234352010857, "id": 87, "parentId": 66, "tags": {}, "startTime": 1750436408302, "traceId": "48385770410509ac"}, {"name": "webpack-close", "duration": 840, "timestamp": 234352017972, "id": 88, "parentId": 66, "tags": {"name": "edge-server"}, "startTime": 1750436408309, "traceId": "48385770410509ac"}, {"name": "webpack-generate-error-stats", "duration": 3768, "timestamp": 234352018873, "id": 89, "parentId": 88, "tags": {}, "startTime": 1750436408310, "traceId": "48385770410509ac"}, {"name": "run-webpack-compiler", "duration": 667036, "timestamp": 234351355756, "id": 66, "parentId": 65, "tags": {}, "startTime": 1750436407647, "traceId": "48385770410509ac"}, {"name": "format-webpack-messages", "duration": 88, "timestamp": 234352022800, "id": 90, "parentId": 65, "tags": {}, "startTime": 1750436408314, "traceId": "48385770410509ac"}, {"name": "worker-main-edge-server", "duration": 667778, "timestamp": 234351355274, "id": 65, "parentId": 1, "tags": {}, "startTime": 1750436407646, "traceId": "48385770410509ac"}, {"name": "create-entrypoints", "duration": 23642, "timestamp": 234353178172, "id": 93, "parentId": 91, "tags": {}, "startTime": 1750436409475, "traceId": "48385770410509ac"}, {"name": "generate-webpack-config", "duration": 489884, "timestamp": 234353202013, "id": 94, "parentId": 92, "tags": {}, "startTime": 1750436409499, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 137904, "timestamp": 234353856876, "id": 105, "parentId": 96, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 249724, "timestamp": 234353856655, "id": 100, "parentId": 96, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 284224, "timestamp": 234353856825, "id": 102, "parentId": 96, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 284290, "timestamp": 234353856796, "id": 101, "parentId": 96, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 292608, "timestamp": 234353856849, "id": 103, "parentId": 96, "tags": {"request": "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 293532, "timestamp": 234353856620, "id": 99, "parentId": 96, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 294063, "timestamp": 234353856571, "id": 98, "parentId": 96, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 298189, "timestamp": 234353856864, "id": 104, "parentId": 96, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CWindows%2011%5C%5CDesktop%5C%5Ccodes%5C%5Ccrm-staff%5C%5Cinno-crm-staff%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5Csidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&server=false!"}, "startTime": 1750436410154, "traceId": "48385770410509ac"}, {"name": "add-entry", "duration": 299389, "timestamp": 234353855706, "id": 97, "parentId": 96, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1750436410153, "traceId": "48385770410509ac"}, {"name": "make", "duration": 301046, "timestamp": 234353854557, "id": 96, "parentId": 95, "tags": {}, "startTime": 1750436410152, "traceId": "48385770410509ac"}, {"name": "chunk-graph", "duration": 9602, "timestamp": 234354177666, "id": 107, "parentId": 106, "tags": {}, "startTime": 1750436410475, "traceId": "48385770410509ac"}, {"name": "optimize-modules", "duration": 33, "timestamp": 234354187410, "id": 109, "parentId": 106, "tags": {}, "startTime": 1750436410484, "traceId": "48385770410509ac"}, {"name": "optimize-chunks", "duration": 7826, "timestamp": 234354187538, "id": 110, "parentId": 106, "tags": {}, "startTime": 1750436410485, "traceId": "48385770410509ac"}, {"name": "optimize-tree", "duration": 133, "timestamp": 234354195513, "id": 111, "parentId": 106, "tags": {}, "startTime": 1750436410493, "traceId": "48385770410509ac"}, {"name": "optimize-chunk-modules", "duration": 7578, "timestamp": 234354195748, "id": 112, "parentId": 106, "tags": {}, "startTime": 1750436410493, "traceId": "48385770410509ac"}, {"name": "optimize", "duration": 16093, "timestamp": 234354187344, "id": 108, "parentId": 106, "tags": {}, "startTime": 1750436410484, "traceId": "48385770410509ac"}, {"name": "module-hash", "duration": 16285, "timestamp": 234354220123, "id": 113, "parentId": 106, "tags": {}, "startTime": 1750436410517, "traceId": "48385770410509ac"}, {"name": "code-generation", "duration": 5211, "timestamp": 234354236475, "id": 114, "parentId": 106, "tags": {}, "startTime": 1750436410533, "traceId": "48385770410509ac"}, {"name": "hash", "duration": 10883, "timestamp": 234354247746, "id": 115, "parentId": 106, "tags": {}, "startTime": 1750436410545, "traceId": "48385770410509ac"}, {"name": "code-generation-jobs", "duration": 320, "timestamp": 234354258624, "id": 116, "parentId": 106, "tags": {}, "startTime": 1750436410556, "traceId": "48385770410509ac"}, {"name": "module-assets", "duration": 250, "timestamp": 234354258898, "id": 117, "parentId": 106, "tags": {}, "startTime": 1750436410556, "traceId": "48385770410509ac"}]