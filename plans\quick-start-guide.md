# Quick Start Guide - Separate Projects Implementation

## Overview
This guide will help you quickly set up the two new CRM projects (Staff and Students) as separate repositories using Next.js 15, TypeScript, and Vercel deployment.

## Prerequisites
- Node.js 18+ installed
- Git configured
- GitHub account
- Vercel account
- Access to the provided Neon database connection strings

## Step 1: Create Staff Project (15 minutes)

### 1.1 Create GitHub Repository
```bash
# Create new repository on GitHub: inno-crm-staff
# Clone and initialize
git clone https://github.com/your-username/inno-crm-staff.git
cd inno-crm-staff
```

### 1.2 Initialize Next.js Project
```bash
# Initialize Next.js 15 with TypeScript
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"

# Install core dependencies
npm install @prisma/client prisma next-auth @next-auth/prisma-adapter
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tabs @radix-ui/react-label
npm install @hookform/resolvers react-hook-form zod
npm install bcryptjs @types/bcryptjs
npm install lucide-react class-variance-authority clsx tailwind-merge
npm install recharts date-fns zustand

# Development dependencies
npm install -D @types/node tsx
```

### 1.3 Setup Environment
```bash
# Create environment file
cat > .env.local << EOF
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require"

NEXTAUTH_SECRET="staff-secret-key-$(openssl rand -base64 32)"
NEXTAUTH_URL="http://localhost:3001"

# Inter-server communication
STUDENTS_SERVER_URL="http://localhost:3002"
STUDENTS_API_KEY="students-api-key"
STUDENTS_SECRET_KEY="students-secret-key"

# VPN Configuration
VPN_REQUIRED="false"
ALLOWED_IP_RANGES="********/24,***********/24,127.0.0.1/32"

# Application Configuration
APP_NAME="Innovative Centre - Staff Portal"
APP_ENV="development"
EOF
```

### 1.4 Setup Prisma
```bash
# Initialize Prisma
npx prisma init

# Copy the staff schema from the implementation guide
# (Copy the content from docs/separate-projects-implementation-guide.md section 2.1)

# Generate Prisma client and push schema
npx prisma generate
npx prisma db push
```

### 1.5 Update Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "prisma generate && next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate",
    "postinstall": "prisma generate"
  }
}
```

### 1.6 Create Vercel Configuration
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "prisma generate && npm run build",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

## Step 2: Create Students Project (15 minutes)

### 2.1 Create GitHub Repository
```bash
# Create new repository on GitHub: inno-crm-students
# Clone and initialize
git clone https://github.com/your-username/inno-crm-students.git
cd inno-crm-students
```

### 2.2 Initialize Next.js Project
```bash
# Initialize Next.js 15 with TypeScript
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"

# Install same dependencies as staff project
npm install @prisma/client prisma next-auth @next-auth/prisma-adapter
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tabs @radix-ui/react-label
npm install @hookform/resolvers react-hook-form zod
npm install bcryptjs @types/bcryptjs
npm install lucide-react class-variance-authority clsx tailwind-merge
npm install recharts date-fns zustand

# Development dependencies
npm install -D @types/node tsx
```

### 2.3 Setup Environment
```bash
# Create environment file
cat > .env.local << EOF
DATABASE_URL="postgresql://crm-students_owner:<EMAIL>/crm-students?sslmode=require"

NEXTAUTH_SECRET="students-secret-key-$(openssl rand -base64 32)"
NEXTAUTH_URL="http://localhost:3002"

# Inter-server communication
STAFF_SERVER_URL="http://localhost:3001"
STAFF_API_KEY="staff-api-key"
STAFF_SECRET_KEY="staff-secret-key"

# Application Configuration
APP_NAME="Innovative Centre - Student Portal"
APP_ENV="development"
EOF
```

### 2.4 Setup Prisma
```bash
# Initialize Prisma
npx prisma init

# Copy the students schema from the implementation guide
# (Copy the content from docs/separate-projects-implementation-guide.md section 2.2)

# Generate Prisma client and push schema
npx prisma generate
npx prisma db push
```

### 2.5 Update Package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev --port 3002",
    "build": "prisma generate && npm run build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate",
    "postinstall": "prisma generate"
  }
}
```

### 2.6 Create Vercel Configuration
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "prisma generate && npm run build",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "SAMEORIGIN"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

## Step 3: Basic Project Structure (10 minutes each)

### 3.1 Create Basic Folder Structure
```bash
# For both projects, create these folders:
mkdir -p lib/{auth,database,utils,api-clients}
mkdir -p components/{ui,forms,tables,charts,dashboard}
mkdir -p app/{api,auth,dashboard}
mkdir -p types
```

### 3.2 Create Basic Database Connection
```typescript
// lib/database.ts (for both projects)
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const db = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db
```

### 3.3 Create Basic Auth Configuration
```typescript
// lib/auth.ts (for both projects, adjust roles accordingly)
import { NextAuthOptions } from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import { db } from './database'
import bcrypt from 'bcryptjs'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        phone: { label: 'Phone', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        const user = await db.user.findUnique({
          where: { phone: credentials.phone }
        })

        if (!user) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role,
        }
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    jwt: async ({ token, user }) => {
      if (user) {
        token.role = user.role
      }
      return token
    },
    session: async ({ session, token }) => {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin'
  }
}
```

## Step 4: Deploy to Vercel (5 minutes each)

### 4.1 Deploy Staff Project
```bash
# In inno-crm-staff directory
git add .
git commit -m "Initial staff project setup"
git push origin main

# Deploy to Vercel
npx vercel --prod
# Follow prompts and set custom domain: staff.innovative-centre.uz
```

### 4.2 Deploy Students Project
```bash
# In inno-crm-students directory
git add .
git commit -m "Initial students project setup"
git push origin main

# Deploy to Vercel
npx vercel --prod
# Follow prompts and set custom domain: students.innovative-centre.uz
```

## Step 5: Test Setup (5 minutes)

### 5.1 Test Local Development
```bash
# Terminal 1 - Staff project
cd inno-crm-staff
npm run dev
# Should run on http://localhost:3001

# Terminal 2 - Students project
cd inno-crm-students
npm run dev
# Should run on http://localhost:3002
```

### 5.2 Verify Database Connections
```bash
# Test staff database
cd inno-crm-staff
npx prisma studio
# Should open Prisma Studio for staff database

# Test students database
cd inno-crm-students
npx prisma studio
# Should open Prisma Studio for students database
```

## Next Steps

1. **Implement Authentication Pages**: Create sign-in/sign-up pages for both projects
2. **Build Dashboard Components**: Create basic dashboard layouts
3. **Implement Inter-Server APIs**: Set up communication between projects
4. **Add Security Middleware**: Implement VPN restrictions for staff project
5. **Create CRUD Operations**: Build the core functionality for each project

## Troubleshooting

### Common Issues:
- **Database Connection**: Ensure connection strings are correct and databases are accessible
- **Port Conflicts**: Make sure ports 3001 and 3002 are available
- **Environment Variables**: Double-check all environment variables are set correctly
- **Prisma Issues**: Run `npx prisma generate` if you encounter Prisma client issues

### Useful Commands:
```bash
# Reset database
npx prisma db push --force-reset

# View database
npx prisma studio

# Check build
npm run build

# Type check
npx tsc --noEmit
```

This quick start guide will get you up and running with both projects in about 1 hour. Once completed, you'll have two separate, modern Next.js applications ready for development and deployment on Vercel.
