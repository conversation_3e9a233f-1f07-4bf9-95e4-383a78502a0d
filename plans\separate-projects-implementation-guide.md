# Separate Projects Implementation Guide

## Overview
This guide provides step-by-step instructions for creating two new separate projects (Staff and Students) using Next.js 15, TypeScript, and Vercel deployment, rather than modifying the existing monolithic CRM system.

## Project Architecture

### New Repository Structure
```
GitHub Repositories:
├── inno-crm-staff/          # New Staff Project
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── prisma/
│   ├── package.json
│   ├── vercel.json
│   └── README.md
├── inno-crm-students/       # New Students Project
│   ├── app/
│   ├── components/
│   ├── lib/
│   ├── prisma/
│   ├── package.json
│   ├── vercel.json
│   └── README.md
└── inno-crm/               # Existing Monolithic System (Keep for reference/migration)
    └── (current structure)
```

## Phase 1: Project Setup

### 1.1 Create Staff Project Repository
```bash
# Create new repository on GitHub: inno-crm-staff
git clone https://github.com/your-username/inno-crm-staff.git
cd inno-crm-staff

# Initialize Next.js 15 project
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"

# Install additional dependencies
npm install @prisma/client prisma next-auth @next-auth/prisma-adapter
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tabs @radix-ui/react-label
npm install @hookform/resolvers react-hook-form zod
npm install bcryptjs @types/bcryptjs
npm install lucide-react class-variance-authority clsx tailwind-merge
npm install recharts date-fns
npm install zustand

# Development dependencies
npm install -D @types/node tsx
```

### 1.2 Create Students Project Repository
```bash
# Create new repository on GitHub: inno-crm-students
git clone https://github.com/your-username/inno-crm-students.git
cd inno-crm-students

# Initialize Next.js 15 project
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir=false --import-alias="@/*"

# Install same dependencies as staff project
npm install @prisma/client prisma next-auth @next-auth/prisma-adapter
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select
npm install @radix-ui/react-toast @radix-ui/react-tabs @radix-ui/react-label
npm install @hookform/resolvers react-hook-form zod
npm install bcryptjs @types/bcryptjs
npm install lucide-react class-variance-authority clsx tailwind-merge
npm install recharts date-fns
npm install zustand

# Development dependencies
npm install -D @types/node tsx
```

## Phase 2: Database Setup

### 2.1 Staff Database Schema (Prisma)
```typescript
// inno-crm-staff/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Staff-specific models
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(ADMIN)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  teacherProfile Teacher?
  activityLogs   ActivityLog[]
  callRecords    CallRecord[]
  messages       Message[]
  announcements  Announcement[]

  @@map("users")
}

model Teacher {
  id          String      @id @default(cuid())
  userId      String      @unique
  subject     String
  experience  Int?
  salary      Decimal?
  branch      String
  photoUrl    String?
  tier        TeacherTier @default(NEW)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  groups       Group[]
  assignedLeads Lead[]

  @@map("teachers")
}

model Group {
  id        String   @id @default(cuid())
  name      String   @unique
  courseId  String
  teacherId String
  capacity  Int      @default(20)
  schedule  Json     // JSON string for schedule
  room      String?
  cabinetId String?
  branch    String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  course         Course            @relation(fields: [courseId], references: [id])
  teacher        Teacher           @relation(fields: [teacherId], references: [id])
  cabinet        Cabinet?          @relation(fields: [cabinetId], references: [id])
  assignedLeads  Lead[]
  studentReferences StudentReference[]

  @@map("groups")
}

model Course {
  id          String   @id @default(cuid())
  name        String   @unique
  level       Level
  description String?
  duration    Int // in weeks
  price       Decimal
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups Group[]

  @@map("courses")
}

model Lead {
  id               String     @id @default(cuid())
  name             String
  phone            String     @unique
  coursePreference String
  status           LeadStatus @default(NEW)
  source           String?
  notes            String?
  branch           String     @default("main")
  assignedTo       String?
  followUpDate     DateTime?
  callStartedAt    DateTime?
  callEndedAt      DateTime?
  callDuration     Int?       // in seconds
  assignedGroupId  String?
  assignedTeacherId String?
  assignedAt       DateTime?
  archivedAt       DateTime?
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  // Relations
  assignedGroup    Group?     @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher  Teacher?   @relation(fields: [assignedTeacherId], references: [id])
  callRecords      CallRecord[]

  @@map("leads")
}

model Cabinet {
  id          String   @id @default(cuid())
  name        String   @unique
  number      String   @unique
  capacity    Int      @default(20)
  floor       Int?
  building    String?
  branch      String
  equipment   Json?    // JSON string for equipment list
  isActive    Boolean  @default(true)
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups      Group[]

  @@map("cabinets")
}

// Reference table for students (minimal data from students server)
model StudentReference {
  id               String        @id // Same ID as in students database
  name             String
  phone            String
  currentGroupId   String?
  status           StudentStatus
  branch           String
  level            Level?
  emergencyContact String?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  lastSyncedAt     DateTime      @default(now())

  // Relations
  currentGroup     Group?        @relation(fields: [currentGroupId], references: [id])
  paymentOverview  PaymentOverview[]

  @@map("student_references")
}

// Financial overview (admin view of all payments)
model PaymentOverview {
  id                  String        @id @default(cuid())
  studentReferenceId  String
  amount              Decimal
  method              PaymentMethod
  status              PaymentStatus @default(PAID)
  description         String?
  transactionId       String?
  dueDate             DateTime?
  paidDate            DateTime?
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Relations
  studentReference    StudentReference @relation(fields: [studentReferenceId], references: [id])

  @@map("payment_overview")
}

model ActivityLog {
  id          String   @id @default(cuid())
  userId      String
  userRole    Role
  action      String   // e.g., "CREATE", "UPDATE", "DELETE", "VIEW"
  resource    String   // e.g., "student", "payment", "group"
  resourceId  String?  // ID of the affected resource
  details     Json?    // Additional details about the action
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model CallRecord {
  id          String   @id @default(cuid())
  leadId      String
  userId      String   // User who made the call
  startedAt   DateTime
  endedAt     DateTime?
  duration    Int?     // in seconds
  notes       String?
  recordingUrl String? // URL to call recording if available
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  lead        Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@map("call_records")
}

model Message {
  id            String        @id @default(cuid())
  subject       String
  content       String
  recipientType String        // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS', 'SPECIFIC'
  recipientIds  String[]      // Array of user IDs for specific recipients
  priority      String        @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  status        String        @default("DRAFT") // 'DRAFT', 'SENT', 'FAILED'
  sentAt        DateTime?
  senderId      String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  sender        User          @relation(fields: [senderId], references: [id])

  @@map("messages")
}

model Announcement {
  id            String        @id @default(cuid())
  title         String
  content       String
  priority      String        @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  targetAudience String       @default("ALL") // 'ALL', 'STUDENTS', 'TEACHERS', 'ACADEMIC_MANAGERS'
  isActive      Boolean       @default(true)
  authorId      String
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  author        User          @relation(fields: [authorId], references: [id])

  @@map("announcements")
}

// Enums
enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  ACADEMIC_MANAGER
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum TeacherTier {
  A_LEVEL
  B_LEVEL
  C_LEVEL
  NEW
}
```

### 2.2 Students Database Schema (Prisma)
```typescript
// inno-crm-students/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Student-specific models
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(STUDENT)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  studentProfile Student?
  messages       Message[]

  @@map("users")
}

model Student {
  id               String        @id @default(cuid())
  userId           String        @unique
  level            Level         @default(A1)
  branch           String
  emergencyContact String?
  photoUrl         String?
  dateOfBirth      DateTime?
  address          String?
  status           StudentStatus @default(ACTIVE)
  currentGroupReferenceId String?
  droppedAt        DateTime?
  pausedAt         DateTime?
  resumedAt        DateTime?
  reEnrollmentNotes String?
  lastContactedAt   DateTime?
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  currentGroupReference GroupReference? @relation("StudentCurrentGroup", fields: [currentGroupReferenceId], references: [id])
  enrollments  Enrollment[]
  payments     Payment[]
  attendances  Attendance[]
  assessments  Assessment[]

  @@map("students")
}

// Reference tables for staff data
model GroupReference {
  id                String   @id // Same ID as in staff database
  name              String
  teacherReferenceId String
  courseName        String
  schedule          Json
  room              String?
  branch            String
  startDate         DateTime
  endDate           DateTime
  isActive          Boolean  @default(true)
  lastSyncedAt      DateTime @default(now())
  syncVersion       Int      @default(1)

  // Relations
  teacherReference  TeacherReference @relation(fields: [teacherReferenceId], references: [id])
  currentStudents   Student[]        @relation("StudentCurrentGroup")
  enrollments       Enrollment[]
  attendances       Attendance[]
  assessments       Assessment[]

  @@map("group_references")
}

model TeacherReference {
  id           String   @id // Same ID as in staff database
  name         String
  subject      String
  branch       String
  photoUrl     String?
  lastSyncedAt DateTime @default(now())
  syncVersion  Int      @default(1)

  // Relations
  groupReferences GroupReference[]

  @@map("teacher_references")
}

model Enrollment {
  id                  String           @id @default(cuid())
  studentId           String
  groupReferenceId    String
  status              EnrollmentStatus @default(ACTIVE)
  startDate           DateTime
  endDate             DateTime?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt

  // Relations
  student             Student          @relation(fields: [studentId], references: [id])
  groupReference      GroupReference   @relation(fields: [groupReferenceId], references: [id])

  @@unique([studentId, groupReferenceId])
  @@map("enrollments")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PAID)
  description   String?
  transactionId String?
  dueDate       DateTime?
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model Attendance {
  id                String           @id @default(cuid())
  studentId         String
  classReferenceId  String           // Reference to class in staff DB
  groupReferenceId  String
  status            AttendanceStatus @default(PRESENT)
  notes             String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  student           Student          @relation(fields: [studentId], references: [id])
  groupReference    GroupReference   @relation(fields: [groupReferenceId], references: [id])

  @@unique([studentId, classReferenceId])
  @@map("attendances")
}

model Assessment {
  id                  String         @id @default(cuid())
  studentId           String?
  groupReferenceId    String?
  testName            String
  type                AssessmentType
  level               Level?
  score               Int?
  maxScore            Int?
  passed              Boolean        @default(false)
  questions           Json?
  results             Json?
  assignedBy          String?        // Teacher/Admin who assigned
  assignedAt          DateTime?
  startedAt           DateTime?
  completedAt         DateTime?
  branch              String         @default("main")
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @updatedAt

  // Relations
  student             Student?       @relation(fields: [studentId], references: [id])
  groupReference      GroupReference? @relation(fields: [groupReferenceId], references: [id])

  @@map("assessments")
}

model Message {
  id                String        @id @default(cuid())
  subject           String
  content           String
  recipientType     String        // 'ALL', 'STUDENTS', 'SPECIFIC'
  recipientIds      String[]      // Array of user IDs for specific recipients
  priority          String        @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  status            String        @default("DRAFT") // 'DRAFT', 'SENT', 'FAILED'
  sentAt            DateTime?
  senderReferenceId String        // Reference to sender in staff DB
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations (for recipients only)
  recipients        User[]        @relation("MessageRecipients")

  @@map("messages")
}

// Enums (same as staff project)
enum Role {
  STUDENT
}

enum Level {
  A1
  A2
  B1
  B2
  IELTS
  SAT
  MATH
  KIDS
}

enum StudentStatus {
  ACTIVE
  DROPPED
  PAUSED
  COMPLETED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum PaymentMethod {
  CASH
  CARD
}

enum PaymentStatus {
  PAID
  DEBT
  REFUNDED
}

enum AssessmentType {
  LEVEL_TEST
  PROGRESS_TEST
  FINAL_EXAM
  GROUP_TEST
}
```

## Phase 3: Environment Configuration

### 3.1 Staff Project Environment
```env
# inno-crm-staff/.env.local
DATABASE_URL="postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require"

NEXTAUTH_SECRET="your-staff-secret-key-here"
NEXTAUTH_URL="https://staff.innovative-centre.uz"

# Inter-server communication
STUDENTS_SERVER_URL="https://students.innovative-centre.uz"
STUDENTS_API_KEY="students-server-api-key"
STUDENTS_SECRET_KEY="students-server-secret-key"

# VPN Configuration
VPN_REQUIRED="true"
ALLOWED_IP_RANGES="********/24,***********/24,127.0.0.1/32"

# Application Configuration
APP_NAME="Innovative Centre - Staff Portal"
APP_ENV="development"
```

### 3.2 Students Project Environment
```env
# inno-crm-students/.env.local
DATABASE_URL="postgresql://crm-students_owner:<EMAIL>/crm-students?sslmode=require"

NEXTAUTH_SECRET="your-students-secret-key-here"
NEXTAUTH_URL="https://students.innovative-centre.uz"

# Inter-server communication
STAFF_SERVER_URL="https://staff.innovative-centre.uz"
STAFF_API_KEY="staff-server-api-key"
STAFF_SECRET_KEY="staff-server-secret-key"

# Application Configuration
APP_NAME="Innovative Centre - Student Portal"
APP_ENV="development"
```

## Phase 4: Vercel Deployment

### 4.1 Staff Project Vercel Configuration
```json
// inno-crm-staff/vercel.json
{
  "framework": "nextjs",
  "buildCommand": "prisma generate && npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ],
  "rewrites": [
    {
      "source": "/api/inter-server/:path*",
      "destination": "/api/inter-server/:path*"
    }
  ]
}
```

### 4.2 Students Project Vercel Configuration
```json
// inno-crm-students/vercel.json
{
  "framework": "nextjs",
  "buildCommand": "prisma generate && npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "SAMEORIGIN"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

## Phase 5: Package.json Scripts

### 5.1 Staff Project Scripts
```json
// inno-crm-staff/package.json scripts section
{
  "scripts": {
    "dev": "next dev --port 3001",
    "build": "prisma generate && next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate",
    "db:migrate": "prisma migrate dev",
    "postinstall": "prisma generate",
    "type-check": "tsc --noEmit"
  }
}
```

### 5.2 Students Project Scripts
```json
// inno-crm-students/package.json scripts section
{
  "scripts": {
    "dev": "next dev --port 3002",
    "build": "prisma generate && next build",
    "start": "next start",
    "lint": "next lint",
    "db:push": "prisma db push",
    "db:studio": "prisma studio",
    "db:generate": "prisma generate",
    "db:migrate": "prisma migrate dev",
    "postinstall": "prisma generate",
    "type-check": "tsc --noEmit"
  }
}
```

This implementation guide provides the foundation for creating two separate, modern Next.js projects that will work together as a distributed CRM system while being deployed on Vercel with proper security and scalability considerations.
