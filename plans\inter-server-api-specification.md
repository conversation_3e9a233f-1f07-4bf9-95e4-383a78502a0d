# Inter-Server Communication API Specification

## Overview
This document defines the secure CRUD APIs for communication between the Staff and Students servers, ensuring data consistency and proper synchronization.

## Authentication & Security

### API Key Authentication
```typescript
interface ApiKeyAuth {
  'X-API-Key': string;           // Server-specific API key
  'X-Server-Source': 'STAFF' | 'STUDENTS' | 'TESTS' | 'IELTS';
  'X-Request-ID': string;        // Unique request identifier for tracking
  'X-Timestamp': string;         // ISO timestamp for request validation
  'X-Signature': string;         // HMAC signature for request integrity
}
```

### Request Signing
```typescript
// HMAC-SHA256 signature generation
function generateSignature(
  method: string,
  path: string,
  body: string,
  timestamp: string,
  secretKey: string
): string {
  const payload = `${method}|${path}|${body}|${timestamp}`;
  return crypto.createHmac('sha256', secretKey).update(payload).digest('hex');
}
```

## Staff Server APIs

### Student Management APIs

#### Create Student
```http
POST /api/inter-server/students
Content-Type: application/json
X-API-Key: {students_server_api_key}
X-Server-Source: STAFF
X-Request-ID: {uuid}
X-Timestamp: {iso_timestamp}
X-Signature: {hmac_signature}

{
  "name": "John Doe",
  "phone": "+************",
  "email": "<EMAIL>",
  "level": "A1",
  "branch": "main",
  "emergencyContact": "+************",
  "dateOfBirth": "2000-01-01T00:00:00Z",
  "address": "123 Main St, Tashkent"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "student_uuid",
    "userId": "user_uuid",
    "name": "John Doe",
    "phone": "+************",
    "email": "<EMAIL>",
    "level": "A1",
    "branch": "main",
    "status": "ACTIVE",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "requestId": "request_uuid"
}
```

#### Update Student
```http
PUT /api/inter-server/students/{studentId}
Content-Type: application/json

{
  "name": "John Smith",
  "level": "A2",
  "status": "ACTIVE",
  "currentGroupId": "group_uuid"
}
```

#### Get Student
```http
GET /api/inter-server/students/{studentId}
```

#### Assign Student to Group
```http
POST /api/inter-server/students/{studentId}/assign-group
Content-Type: application/json

{
  "groupId": "group_uuid",
  "startDate": "2024-01-15T00:00:00Z",
  "notes": "Regular enrollment"
}
```

### Group Management APIs

#### Sync Group Data
```http
POST /api/inter-server/groups/sync
Content-Type: application/json

{
  "groupId": "group_uuid",
  "name": "IELTS Morning Group",
  "teacherId": "teacher_uuid",
  "teacherName": "Jane Smith",
  "courseName": "IELTS Preparation",
  "schedule": {
    "days": ["Monday", "Wednesday", "Friday"],
    "time": "09:00-10:30",
    "timezone": "Asia/Tashkent"
  },
  "room": "Room 101",
  "branch": "main",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-04-01T00:00:00Z",
  "isActive": true
}
```

### Payment Management APIs

#### Record Payment
```http
POST /api/inter-server/payments
Content-Type: application/json

{
  "studentId": "student_uuid",
  "amount": 500000,
  "method": "CASH",
  "status": "PAID",
  "description": "Monthly tuition fee",
  "transactionId": "TXN_123456",
  "paidDate": "2024-01-01T00:00:00Z"
}
```

#### Update Payment Status
```http
PUT /api/inter-server/payments/{paymentId}
Content-Type: application/json

{
  "status": "REFUNDED",
  "notes": "Student withdrawal"
}
```

### Message Broadcasting APIs

#### Send Message to Students
```http
POST /api/inter-server/messages/broadcast
Content-Type: application/json

{
  "subject": "Important Announcement",
  "content": "Classes will resume on Monday.",
  "recipientType": "STUDENTS",
  "recipientIds": ["student1_uuid", "student2_uuid"],
  "priority": "HIGH",
  "senderId": "admin_uuid",
  "senderName": "Admin User"
}
```

## Students Server APIs

### Student Profile APIs

#### Get Student Profile
```http
GET /api/inter-server/profile/{studentId}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "student_uuid",
    "name": "John Doe",
    "phone": "+************",
    "email": "<EMAIL>",
    "level": "A1",
    "branch": "main",
    "status": "ACTIVE",
    "currentGroup": {
      "id": "group_uuid",
      "name": "IELTS Morning Group",
      "teacher": "Jane Smith",
      "schedule": "Mon/Wed/Fri 09:00-10:30"
    },
    "enrollments": [...],
    "payments": [...],
    "attendances": [...]
  }
}
```

#### Update Student Profile
```http
PUT /api/inter-server/profile/{studentId}
Content-Type: application/json

{
  "emergencyContact": "+************",
  "address": "456 New St, Tashkent",
  "photoUrl": "https://example.com/photo.jpg"
}
```

### Attendance APIs

#### Mark Attendance
```http
POST /api/inter-server/attendance
Content-Type: application/json

{
  "studentId": "student_uuid",
  "classId": "class_uuid",
  "status": "PRESENT",
  "notes": "On time"
}
```

#### Get Attendance Records
```http
GET /api/inter-server/attendance/{studentId}?from=2024-01-01&to=2024-01-31
```

### Assessment APIs

#### Create Assessment
```http
POST /api/inter-server/assessments
Content-Type: application/json

{
  "studentId": "student_uuid",
  "testName": "Level Assessment",
  "type": "LEVEL_TEST",
  "level": "A1",
  "maxScore": 100,
  "assignedBy": "teacher_uuid",
  "assignedAt": "2024-01-01T00:00:00Z"
}
```

#### Submit Assessment Results
```http
PUT /api/inter-server/assessments/{assessmentId}/results
Content-Type: application/json

{
  "score": 85,
  "passed": true,
  "results": {
    "listening": 20,
    "reading": 22,
    "writing": 21,
    "speaking": 22
  },
  "completedAt": "2024-01-01T10:00:00Z"
}
```

## Error Handling

### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid student data provided",
    "details": {
      "field": "phone",
      "reason": "Phone number already exists"
    }
  },
  "requestId": "request_uuid",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Codes
```typescript
enum ApiErrorCode {
  // Authentication errors
  INVALID_API_KEY = 'INVALID_API_KEY',
  INVALID_SIGNATURE = 'INVALID_SIGNATURE',
  REQUEST_EXPIRED = 'REQUEST_EXPIRED',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  INVALID_DATA_FORMAT = 'INVALID_DATA_FORMAT',
  
  // Resource errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS = 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT = 'RESOURCE_CONFLICT',
  
  // System errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}
```

## Data Synchronization

### Sync Events
```typescript
interface SyncEvent {
  eventId: string;
  eventType: 'CREATE' | 'UPDATE' | 'DELETE';
  resourceType: 'STUDENT' | 'GROUP' | 'PAYMENT' | 'TEACHER';
  resourceId: string;
  data: any;
  timestamp: string;
  sourceServer: string;
  version: number;
}
```

### Webhook Notifications
```http
POST /api/webhooks/sync
Content-Type: application/json
X-Webhook-Signature: {hmac_signature}

{
  "eventId": "event_uuid",
  "eventType": "UPDATE",
  "resourceType": "STUDENT",
  "resourceId": "student_uuid",
  "data": {
    "status": "DROPPED",
    "droppedAt": "2024-01-01T00:00:00Z"
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "sourceServer": "STAFF",
  "version": 5
}
```

## Rate Limiting

### Rate Limit Headers
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 3600
```

### Rate Limit Configuration
```typescript
interface RateLimitConfig {
  windowMs: number;        // Time window in milliseconds
  maxRequests: number;     // Maximum requests per window
  skipSuccessfulRequests: boolean;
  skipFailedRequests: boolean;
  keyGenerator: (req: Request) => string;
}

// Per-server rate limits
const rateLimits = {
  STAFF: { windowMs: 60000, maxRequests: 1000 },      // 1000 req/min
  STUDENTS: { windowMs: 60000, maxRequests: 500 },    // 500 req/min
  TESTS: { windowMs: 60000, maxRequests: 200 },       // 200 req/min
  IELTS: { windowMs: 60000, maxRequests: 200 }        // 200 req/min
};
```

## Monitoring & Logging

### Request Logging
```typescript
interface ApiRequestLog {
  requestId: string;
  method: string;
  path: string;
  sourceServer: string;
  targetServer: string;
  statusCode: number;
  responseTime: number;
  timestamp: string;
  userAgent?: string;
  ipAddress?: string;
  errorCode?: string;
  errorMessage?: string;
}
```

### Health Check Endpoints
```http
GET /api/health/inter-server
```

**Response:**
```json
{
  "status": "healthy",
  "checks": {
    "database": { "status": "ok", "responseTime": 15 },
    "externalApis": { "status": "ok", "responseTime": 120 },
    "authentication": { "status": "ok" }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

## Implementation Examples

### API Client Class
```typescript
class InterServerApiClient {
  private baseUrl: string;
  private apiKey: string;
  private secretKey: string;
  private serverSource: string;

  constructor(config: ApiClientConfig) {
    this.baseUrl = config.baseUrl;
    this.apiKey = config.apiKey;
    this.secretKey = config.secretKey;
    this.serverSource = config.serverSource;
  }

  async request<T>(
    method: string,
    path: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const requestId = generateUUID();
    const timestamp = new Date().toISOString();
    const body = data ? JSON.stringify(data) : '';
    const signature = this.generateSignature(method, path, body, timestamp);

    const response = await fetch(`${this.baseUrl}${path}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Server-Source': this.serverSource,
        'X-Request-ID': requestId,
        'X-Timestamp': timestamp,
        'X-Signature': signature,
      },
      body: body || undefined,
    });

    if (!response.ok) {
      throw new ApiError(await response.json());
    }

    return response.json();
  }

  private generateSignature(
    method: string,
    path: string,
    body: string,
    timestamp: string
  ): string {
    const payload = `${method}|${path}|${body}|${timestamp}`;
    return crypto.createHmac('sha256', this.secretKey)
      .update(payload)
      .digest('hex');
  }
}
```

### Middleware Implementation
```typescript
export function createInterServerAuthMiddleware(config: AuthConfig) {
  return async (req: NextRequest) => {
    const apiKey = req.headers.get('X-API-Key');
    const serverSource = req.headers.get('X-Server-Source');
    const requestId = req.headers.get('X-Request-ID');
    const timestamp = req.headers.get('X-Timestamp');
    const signature = req.headers.get('X-Signature');

    // Validate required headers
    if (!apiKey || !serverSource || !requestId || !timestamp || !signature) {
      return NextResponse.json(
        { error: 'Missing required headers' },
        { status: 400 }
      );
    }

    // Validate API key
    if (!config.validApiKeys.includes(apiKey)) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      );
    }

    // Validate timestamp (prevent replay attacks)
    const requestTime = new Date(timestamp).getTime();
    const currentTime = Date.now();
    if (Math.abs(currentTime - requestTime) > 300000) { // 5 minutes
      return NextResponse.json(
        { error: 'Request expired' },
        { status: 401 }
      );
    }

    // Validate signature
    const body = await req.text();
    const expectedSignature = generateSignature(
      req.method,
      req.nextUrl.pathname,
      body,
      timestamp,
      config.secretKey
    );

    if (signature !== expectedSignature) {
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      );
    }

    // Add metadata to request
    req.serverSource = serverSource;
    req.requestId = requestId;

    return NextResponse.next();
  };
}
```

This API specification ensures secure, reliable, and efficient communication between the distributed CRM servers while maintaining data consistency and proper error handling.
