# Database Schema Separation Plan

## Overview
This document outlines the strategy for separating the current monolithic database into specialized databases for Staff and Students servers, ensuring data integrity and proper relationships.

## Current Database Analysis

### Existing Entities and Their Dependencies
```
Users (Central entity)
├── Students (1:1 with Users where role=STUDENT)
├── Teachers (1:1 with Users where role=TEACHER/ADMIN/etc.)
├── ActivityLogs (N:1 with Users)
├── CallRecords (N:1 with Users)
├── Messages (N:1 with Users)
└── Announcements (N:1 with Users)

Students
├── Enrollments (N:1 with Students)
├── Payments (N:1 with Students)
├── Attendances (N:1 with Students)
├── Assessments (N:1 with Students)
└── CurrentGroup (N:1 with Groups)

Teachers
├── Groups (N:1 with Teachers)
├── Classes (N:1 with Teachers)
└── AssignedLeads (N:1 with Teachers)

Groups
├── Course (N:1 with Courses)
├── Teacher (N:1 with Teachers)
├── Cabinet (N:1 with Cabinets)
├── CurrentStudents (1:N with Students)
├── Enrollments (1:N with Enrollments)
├── Classes (1:N with Classes)
├── Assessments (1:N with Assessments)
└── AssignedLeads (1:N with Leads)
```

## Separation Strategy

### Staff Database (crm-staff)
**Purpose**: Administrative operations, staff management, financial oversight
**Connection String**: `postgresql://crm-staff_owner:<EMAIL>/crm-staff?sslmode=require`

#### Core Entities (Full Data)
```sql
-- Authentication and Staff Management
CREATE TABLE users (
  id VARCHAR PRIMARY KEY,
  email VARCHAR UNIQUE,
  phone VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  role user_role NOT NULL CHECK (role IN ('ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'ACADEMIC_MANAGER')),
  password VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Staff Profiles
CREATE TABLE teachers (
  id VARCHAR PRIMARY KEY,
  user_id VARCHAR UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  subject VARCHAR NOT NULL,
  experience INTEGER,
  salary DECIMAL,
  branch VARCHAR NOT NULL,
  photo_url VARCHAR,
  tier teacher_tier DEFAULT 'NEW',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Academic Management
CREATE TABLE courses (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  level course_level NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL, -- in weeks
  price DECIMAL NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE groups (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  course_id VARCHAR NOT NULL REFERENCES courses(id),
  teacher_id VARCHAR NOT NULL REFERENCES teachers(id),
  capacity INTEGER DEFAULT 20,
  schedule JSONB NOT NULL, -- JSON string for schedule
  room VARCHAR,
  cabinet_id VARCHAR REFERENCES cabinets(id),
  branch VARCHAR NOT NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Infrastructure Management
CREATE TABLE cabinets (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  number VARCHAR UNIQUE NOT NULL,
  capacity INTEGER DEFAULT 20,
  floor INTEGER,
  building VARCHAR,
  branch VARCHAR NOT NULL,
  equipment JSONB, -- JSON string for equipment list
  is_active BOOLEAN DEFAULT true,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE cabinet_schedules (
  id VARCHAR PRIMARY KEY,
  cabinet_id VARCHAR NOT NULL REFERENCES cabinets(id) ON DELETE CASCADE,
  group_id VARCHAR REFERENCES groups(id) ON DELETE SET NULL,
  day_of_week INTEGER NOT NULL, -- 0 = Sunday, 1 = Monday, etc.
  start_time VARCHAR NOT NULL, -- HH:MM format
  end_time VARCHAR NOT NULL, -- HH:MM format
  title VARCHAR,
  is_blocked BOOLEAN DEFAULT false, -- For maintenance or other blocks
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Lead Management
CREATE TABLE leads (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  phone VARCHAR UNIQUE NOT NULL,
  course_preference VARCHAR NOT NULL,
  status lead_status DEFAULT 'NEW',
  source VARCHAR,
  notes TEXT,
  branch VARCHAR DEFAULT 'main',
  assigned_to VARCHAR REFERENCES users(id),
  follow_up_date TIMESTAMP,
  call_started_at TIMESTAMP,
  call_ended_at TIMESTAMP,
  call_duration INTEGER, -- in seconds
  assigned_group_id VARCHAR REFERENCES groups(id),
  assigned_teacher_id VARCHAR REFERENCES teachers(id),
  assigned_at TIMESTAMP,
  archived_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE call_records (
  id VARCHAR PRIMARY KEY,
  lead_id VARCHAR NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
  user_id VARCHAR NOT NULL REFERENCES users(id),
  started_at TIMESTAMP NOT NULL,
  ended_at TIMESTAMP,
  duration INTEGER, -- in seconds
  notes TEXT,
  recording_url VARCHAR, -- URL to call recording if available
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Administrative Oversight
CREATE TABLE activity_logs (
  id VARCHAR PRIMARY KEY,
  user_id VARCHAR NOT NULL REFERENCES users(id),
  user_role user_role NOT NULL,
  action VARCHAR NOT NULL, -- e.g., "CREATE", "UPDATE", "DELETE", "VIEW"
  resource VARCHAR NOT NULL, -- e.g., "student", "payment", "group"
  resource_id VARCHAR, -- ID of the affected resource
  details JSONB, -- Additional details about the action
  ip_address VARCHAR,
  user_agent VARCHAR,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Financial Overview (Admin view of all payments)
CREATE TABLE payment_overview (
  id VARCHAR PRIMARY KEY,
  student_reference_id VARCHAR NOT NULL, -- Reference to student in students DB
  amount DECIMAL NOT NULL,
  method payment_method NOT NULL,
  status payment_status DEFAULT 'PAID',
  description TEXT,
  transaction_id VARCHAR,
  due_date TIMESTAMP,
  paid_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Reference Tables (Minimal Student Data)
```sql
-- Student references for staff operations
CREATE TABLE student_references (
  id VARCHAR PRIMARY KEY, -- Same ID as in students database
  name VARCHAR NOT NULL,
  phone VARCHAR NOT NULL,
  current_group_id VARCHAR REFERENCES groups(id),
  status student_status NOT NULL,
  branch VARCHAR NOT NULL,
  level course_level,
  emergency_contact VARCHAR,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  -- Sync metadata
  last_synced_at TIMESTAMP DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1
);

-- Enrollment references for group management
CREATE TABLE enrollment_references (
  id VARCHAR PRIMARY KEY,
  student_reference_id VARCHAR NOT NULL REFERENCES student_references(id),
  group_id VARCHAR NOT NULL REFERENCES groups(id),
  status enrollment_status DEFAULT 'ACTIVE',
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Students Database (crm-students)
**Purpose**: Student portal, learning management, student-specific data
**Connection String**: `postgresql://crm-students_owner:<EMAIL>/crm-students?sslmode=require`

#### Core Entities (Full Data)
```sql
-- Student Authentication
CREATE TABLE users (
  id VARCHAR PRIMARY KEY,
  email VARCHAR UNIQUE,
  phone VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  role user_role NOT NULL CHECK (role = 'STUDENT'),
  password VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Profiles
CREATE TABLE students (
  id VARCHAR PRIMARY KEY,
  user_id VARCHAR UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  level course_level DEFAULT 'A1',
  branch VARCHAR NOT NULL,
  emergency_contact VARCHAR,
  photo_url VARCHAR,
  date_of_birth TIMESTAMP,
  address TEXT,
  status student_status DEFAULT 'ACTIVE',
  current_group_reference_id VARCHAR, -- Reference to group in staff DB
  dropped_at TIMESTAMP,
  paused_at TIMESTAMP,
  resumed_at TIMESTAMP,
  re_enrollment_notes TEXT,
  last_contacted_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Academic Records
CREATE TABLE enrollments (
  id VARCHAR PRIMARY KEY,
  student_id VARCHAR NOT NULL REFERENCES students(id),
  group_reference_id VARCHAR NOT NULL, -- Reference to group in staff DB
  status enrollment_status DEFAULT 'ACTIVE',
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(student_id, group_reference_id)
);

CREATE TABLE attendances (
  id VARCHAR PRIMARY KEY,
  student_id VARCHAR NOT NULL REFERENCES students(id),
  class_reference_id VARCHAR NOT NULL, -- Reference to class in staff DB
  status attendance_status DEFAULT 'PRESENT',
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(student_id, class_reference_id)
);

-- Student Payments
CREATE TABLE payments (
  id VARCHAR PRIMARY KEY,
  student_id VARCHAR NOT NULL REFERENCES students(id),
  amount DECIMAL NOT NULL,
  method payment_method NOT NULL,
  status payment_status DEFAULT 'PAID',
  description TEXT,
  transaction_id VARCHAR,
  due_date TIMESTAMP,
  paid_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Assessments
CREATE TABLE assessments (
  id VARCHAR PRIMARY KEY,
  student_id VARCHAR REFERENCES students(id),
  group_reference_id VARCHAR, -- Reference to group in staff DB
  test_name VARCHAR NOT NULL,
  type assessment_type NOT NULL,
  level course_level,
  score INTEGER,
  max_score INTEGER,
  passed BOOLEAN DEFAULT false,
  questions JSONB, -- Store questions and answers
  results JSONB, -- Store detailed results
  assigned_by VARCHAR, -- Teacher/Admin who assigned
  assigned_at TIMESTAMP,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  branch VARCHAR DEFAULT 'main',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Communication
CREATE TABLE messages (
  id VARCHAR PRIMARY KEY,
  subject VARCHAR NOT NULL,
  content TEXT NOT NULL,
  recipient_type VARCHAR NOT NULL, -- 'ALL', 'STUDENTS', 'SPECIFIC'
  recipient_ids VARCHAR[], -- Array of user IDs for specific recipients
  priority VARCHAR DEFAULT 'MEDIUM', -- 'LOW', 'MEDIUM', 'HIGH'
  status VARCHAR DEFAULT 'DRAFT', -- 'DRAFT', 'SENT', 'FAILED'
  sent_at TIMESTAMP,
  sender_reference_id VARCHAR NOT NULL, -- Reference to sender in staff DB
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE announcements (
  id VARCHAR PRIMARY KEY,
  title VARCHAR NOT NULL,
  content TEXT NOT NULL,
  priority VARCHAR DEFAULT 'MEDIUM', -- 'LOW', 'MEDIUM', 'HIGH'
  target_audience VARCHAR DEFAULT 'ALL', -- 'ALL', 'STUDENTS'
  is_active BOOLEAN DEFAULT true,
  author_reference_id VARCHAR NOT NULL, -- Reference to author in staff DB
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Reference Tables (Minimal Staff Data)
```sql
-- Group references for student operations
CREATE TABLE group_references (
  id VARCHAR PRIMARY KEY, -- Same ID as in staff database
  name VARCHAR NOT NULL,
  teacher_reference_id VARCHAR NOT NULL,
  course_name VARCHAR NOT NULL,
  schedule JSONB NOT NULL,
  room VARCHAR,
  branch VARCHAR NOT NULL,
  start_date TIMESTAMP NOT NULL,
  end_date TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT true,
  
  -- Sync metadata
  last_synced_at TIMESTAMP DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1
);

-- Teacher references for student view
CREATE TABLE teacher_references (
  id VARCHAR PRIMARY KEY, -- Same ID as in staff database
  name VARCHAR NOT NULL,
  subject VARCHAR NOT NULL,
  branch VARCHAR NOT NULL,
  photo_url VARCHAR,
  
  -- Sync metadata
  last_synced_at TIMESTAMP DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1
);

-- Course references for student information
CREATE TABLE course_references (
  id VARCHAR PRIMARY KEY, -- Same ID as in staff database
  name VARCHAR NOT NULL,
  level course_level NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL,
  price DECIMAL NOT NULL,
  
  -- Sync metadata
  last_synced_at TIMESTAMP DEFAULT NOW(),
  sync_version INTEGER DEFAULT 1
);

-- Class references for attendance tracking
CREATE TABLE class_references (
  id VARCHAR PRIMARY KEY, -- Same ID as in staff database
  group_reference_id VARCHAR NOT NULL REFERENCES group_references(id),
  teacher_reference_id VARCHAR NOT NULL REFERENCES teacher_references(id),
  date TIMESTAMP NOT NULL,
  topic VARCHAR,
  homework TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Data Migration Strategy

### Phase 1: Schema Creation
```sql
-- Create both databases with new schemas
-- Run migration scripts to create tables and indexes
-- Set up proper constraints and relationships
```

### Phase 2: Data Migration
```javascript
// Migration script structure
async function migrateData() {
  // 1. Migrate staff-related data to staff database
  await migrateStaffUsers();
  await migrateTeachers();
  await migrateGroups();
  await migrateCourses();
  await migrateLeads();
  await migrateCabinets();
  
  // 2. Migrate student-related data to students database
  await migrateStudentUsers();
  await migrateStudents();
  await migrateEnrollments();
  await migratePayments();
  await migrateAttendances();
  await migrateAssessments();
  
  // 3. Create reference tables
  await createStudentReferences();
  await createGroupReferences();
  await createTeacherReferences();
  
  // 4. Verify data integrity
  await verifyMigration();
}
```

### Phase 3: Synchronization Setup
```javascript
// Real-time sync mechanisms
class DataSynchronizer {
  async syncStudentToStaff(studentData) {
    // Update student reference in staff database
    await staffDb.studentReference.upsert({
      where: { id: studentData.id },
      update: {
        name: studentData.name,
        phone: studentData.phone,
        status: studentData.status,
        currentGroupId: studentData.currentGroupId,
        lastSyncedAt: new Date(),
        syncVersion: { increment: 1 }
      },
      create: { ...studentData, lastSyncedAt: new Date(), syncVersion: 1 }
    });
  }
  
  async syncGroupToStudents(groupData) {
    // Update group reference in students database
    await studentsDb.groupReference.upsert({
      where: { id: groupData.id },
      update: {
        name: groupData.name,
        teacherReferenceId: groupData.teacherId,
        courseName: groupData.course.name,
        schedule: groupData.schedule,
        room: groupData.room,
        branch: groupData.branch,
        lastSyncedAt: new Date(),
        syncVersion: { increment: 1 }
      },
      create: { ...groupData, lastSyncedAt: new Date(), syncVersion: 1 }
    });
  }
}
```

## Enums and Types
```sql
-- Shared enums across both databases
CREATE TYPE user_role AS ENUM ('ADMIN', 'MANAGER', 'TEACHER', 'RECEPTION', 'CASHIER', 'STUDENT', 'ACADEMIC_MANAGER');
CREATE TYPE course_level AS ENUM ('A1', 'A2', 'B1', 'B2', 'IELTS', 'SAT', 'MATH', 'KIDS');
CREATE TYPE lead_status AS ENUM ('NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'ARCHIVED', 'NOT_INTERESTED');
CREATE TYPE student_status AS ENUM ('ACTIVE', 'DROPPED', 'PAUSED', 'COMPLETED');
CREATE TYPE enrollment_status AS ENUM ('ACTIVE', 'COMPLETED', 'DROPPED', 'SUSPENDED');
CREATE TYPE attendance_status AS ENUM ('PRESENT', 'ABSENT', 'LATE', 'EXCUSED');
CREATE TYPE payment_method AS ENUM ('CASH', 'CARD');
CREATE TYPE payment_status AS ENUM ('PAID', 'DEBT', 'REFUNDED');
CREATE TYPE assessment_type AS ENUM ('LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'GROUP_TEST');
CREATE TYPE teacher_tier AS ENUM ('A_LEVEL', 'B_LEVEL', 'C_LEVEL', 'NEW');
```

## Data Integrity Measures

### Foreign Key Handling
- Use reference tables to maintain relationships across databases
- Implement soft deletes for critical entities
- Add sync metadata for tracking data consistency

### Conflict Resolution
- Use timestamp-based conflict resolution
- Implement version numbers for optimistic locking
- Log all sync operations for audit trails

### Backup Strategy
- Daily automated backups for both databases
- Cross-database consistency checks
- Point-in-time recovery capabilities

This separation strategy ensures that each server has access to the data it needs while maintaining proper relationships and data integrity across the distributed system.
