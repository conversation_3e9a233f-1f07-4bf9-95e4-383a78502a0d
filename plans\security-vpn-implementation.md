# Security & VPN Implementation Plan

## Overview
This document outlines the comprehensive security implementation for the multi-server CRM architecture, focusing on VPN-based access control for the staff server and secure inter-server communication.

## VPN Infrastructure

### VPN Server Setup (OpenVPN)

#### 1. VPN Server Installation
```bash
#!/bin/bash
# install-vpn-server.sh

# Update system
sudo apt update && sudo apt upgrade -y

# Install OpenVPN and Easy-RSA
sudo apt install openvpn easy-rsa -y

# Create CA directory
sudo make-cadir /etc/openvpn/easy-rsa
cd /etc/openvpn/easy-rsa

# Initialize PKI
sudo ./easyrsa init-pki

# Build CA
sudo ./easyrsa build-ca nopass

# Generate server certificate
sudo ./easyrsa gen-req server nopass
sudo ./easyrsa sign-req server server

# Generate Diffie-Hellman parameters
sudo ./easyrsa gen-dh

# Generate TLS-auth key
sudo openvpn --genkey --secret /etc/openvpn/ta.key

# Copy certificates
sudo cp pki/ca.crt /etc/openvpn/
sudo cp pki/issued/server.crt /etc/openvpn/
sudo cp pki/private/server.key /etc/openvpn/
sudo cp pki/dh.pem /etc/openvpn/
```

#### 2. VPN Server Configuration
```bash
# /etc/openvpn/server.conf
port 1194
proto udp
dev tun

# SSL/TLS root certificate (ca), certificate (cert), and private key (key)
ca ca.crt
cert server.crt
key server.key
dh dh.pem

# Network topology
topology subnet

# Configure server mode and supply a VPN subnet
server ******** *************

# Maintain a record of client <-> virtual IP address associations
ifconfig-pool-persist /var/log/openvpn/ipp.txt

# Push routes to the client to allow it to reach other private subnets
push "route *********** *************"
push "route 10.0.0.0 *********"

# DNS servers to push to clients
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Enable compression
comp-lzo

# The maximum number of concurrently connected clients
max-clients 100

# Reduce the OpenVPN daemon's privileges after initialization
user nobody
group nogroup

# The persist options will try to avoid accessing certain resources
persist-key
persist-tun

# Output a short status file showing current connections
status /var/log/openvpn/openvpn-status.log

# Log verbosity level
verb 3

# Silence repeating messages
mute 20

# TLS-auth for additional security
tls-auth ta.key 0

# Cipher and authentication
cipher AES-256-CBC
auth SHA256

# Enable TLS 1.2 minimum
tls-version-min 1.2
```

#### 3. Client Certificate Generation
```bash
#!/bin/bash
# generate-client-cert.sh

CLIENT_NAME=$1

if [ -z "$CLIENT_NAME" ]; then
    echo "Usage: $0 <client_name>"
    exit 1
fi

cd /etc/openvpn/easy-rsa

# Generate client certificate
sudo ./easyrsa gen-req $CLIENT_NAME nopass
sudo ./easyrsa sign-req client $CLIENT_NAME

# Create client configuration directory
sudo mkdir -p /etc/openvpn/clients/$CLIENT_NAME

# Generate client configuration file
cat > /etc/openvpn/clients/$CLIENT_NAME/$CLIENT_NAME.ovpn << EOF
client
dev tun
proto udp
remote your-vpn-server.com 1194
resolv-retry infinite
nobind
persist-key
persist-tun
ca ca.crt
cert $CLIENT_NAME.crt
key $CLIENT_NAME.key
tls-auth ta.key 1
cipher AES-256-CBC
auth SHA256
comp-lzo
verb 3
mute 20
EOF

# Copy certificates to client directory
sudo cp pki/ca.crt /etc/openvpn/clients/$CLIENT_NAME/
sudo cp pki/issued/$CLIENT_NAME.crt /etc/openvpn/clients/$CLIENT_NAME/
sudo cp pki/private/$CLIENT_NAME.key /etc/openvpn/clients/$CLIENT_NAME/
sudo cp ta.key /etc/openvpn/clients/$CLIENT_NAME/

echo "Client configuration created: /etc/openvpn/clients/$CLIENT_NAME/"
```

### VPN Network Configuration

#### IP Address Ranges
```
VPN Network: ********/24
- VPN Server: ********
- Staff Clients: ********0 - *********
- Admin Clients: ********* - ********00
- Service IPs: ********01 - **********

Internal Network: ***********/24
- Staff Server: ************
- Database Server: ************
- Load Balancer: ************
```

## Staff Server Security Implementation

### 1. IP-Based Access Control Middleware
```typescript
// servers/staff/middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { isIPInRange } from '@/lib/ip-utils';

// Allowed IP ranges for staff server access
const ALLOWED_IP_RANGES = [
  '********/24',        // VPN network
  '***********/24',     // Internal network
  '127.0.0.1/32',       // Localhost
  '::1/128',            // IPv6 localhost
];

// Emergency access IPs (for maintenance)
const EMERGENCY_IPS = [
  '************',       // Admin home IP
  '************',       // Office backup IP
];

export async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Skip IP check for health checks and public assets
  if (pathname.startsWith('/api/health') || 
      pathname.startsWith('/_next/') || 
      pathname.startsWith('/favicon.ico')) {
    return NextResponse.next();
  }

  // Get client IP
  const clientIP = getClientIP(request);
  
  // Skip IP check in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`[DEV] Would check IP: ${clientIP}`);
    return NextResponse.next();
  }

  // Check if IP is allowed
  if (!isIPAllowed(clientIP)) {
    // Log unauthorized access attempt
    await logSecurityEvent({
      type: 'UNAUTHORIZED_ACCESS_ATTEMPT',
      ip: clientIP,
      path: pathname,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });

    return new NextResponse(
      JSON.stringify({
        error: 'Access Denied',
        message: 'VPN connection required to access this service',
        code: 'VPN_REQUIRED'
      }),
      {
        status: 403,
        headers: {
          'Content-Type': 'application/json',
          'X-Access-Denied-Reason': 'IP_NOT_ALLOWED'
        }
      }
    );
  }

  // Add security headers
  const response = NextResponse.next();
  addSecurityHeaders(response);
  
  return response;
}

function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const xForwardedFor = request.headers.get('x-forwarded-for');
  const xRealIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (xForwardedFor) {
    return xForwardedFor.split(',')[0].trim();
  }
  
  if (xRealIP) {
    return xRealIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return request.ip || '127.0.0.1';
}

function isIPAllowed(ip: string): boolean {
  // Check allowed ranges
  for (const range of ALLOWED_IP_RANGES) {
    if (isIPInRange(ip, range)) {
      return true;
    }
  }
  
  // Check emergency IPs
  if (EMERGENCY_IPS.includes(ip)) {
    return true;
  }
  
  return false;
}

function addSecurityHeaders(response: NextResponse): void {
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  );
}

export const config = {
  matcher: [
    '/((?!api/health|_next/static|_next/image|favicon.ico).*)',
  ],
};
```

### 2. IP Utility Functions
```typescript
// lib/ip-utils.ts
import { Address4, Address6 } from 'ip-address';

export function isIPInRange(ip: string, cidr: string): boolean {
  try {
    if (ip.includes(':')) {
      // IPv6
      const address = new Address6(ip);
      const subnet = new Address6(cidr);
      return address.isInSubnet(subnet);
    } else {
      // IPv4
      const address = new Address4(ip);
      const subnet = new Address4(cidr);
      return address.isInSubnet(subnet);
    }
  } catch (error) {
    console.error('IP validation error:', error);
    return false;
  }
}

export function normalizeIP(ip: string): string {
  // Handle IPv4-mapped IPv6 addresses
  if (ip.startsWith('::ffff:')) {
    return ip.substring(7);
  }
  return ip;
}

export function isPrivateIP(ip: string): boolean {
  const privateRanges = [
    '10.0.0.0/8',
    '**********/12',
    '***********/16',
    '*********/8',
    '***********/16'
  ];
  
  return privateRanges.some(range => isIPInRange(ip, range));
}
```

### 3. Security Event Logging
```typescript
// lib/security-logger.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface SecurityEvent {
  type: string;
  ip: string;
  path?: string;
  userAgent?: string;
  userId?: string;
  details?: any;
  timestamp: string;
}

export async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    await prisma.securityLog.create({
      data: {
        type: event.type,
        ipAddress: event.ip,
        path: event.path,
        userAgent: event.userAgent,
        userId: event.userId,
        details: event.details,
        timestamp: new Date(event.timestamp),
      }
    });

    // Send alert for critical events
    if (isCriticalEvent(event.type)) {
      await sendSecurityAlert(event);
    }
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
}

function isCriticalEvent(eventType: string): boolean {
  const criticalEvents = [
    'UNAUTHORIZED_ACCESS_ATTEMPT',
    'BRUTE_FORCE_ATTACK',
    'SQL_INJECTION_ATTEMPT',
    'XSS_ATTEMPT',
    'PRIVILEGE_ESCALATION'
  ];
  
  return criticalEvents.includes(eventType);
}

async function sendSecurityAlert(event: SecurityEvent): Promise<void> {
  // Send email/SMS alert to administrators
  // Implementation depends on your notification system
  console.log('SECURITY ALERT:', event);
}
```

## Inter-Server Security

### 1. API Key Management
```typescript
// lib/api-key-manager.ts
import crypto from 'crypto';

interface ApiKeyConfig {
  serverId: string;
  keyId: string;
  secretKey: string;
  permissions: string[];
  expiresAt?: Date;
  isActive: boolean;
}

export class ApiKeyManager {
  private keys: Map<string, ApiKeyConfig> = new Map();

  constructor() {
    this.loadApiKeys();
  }

  private loadApiKeys(): void {
    // Load from environment or secure storage
    const staffKey: ApiKeyConfig = {
      serverId: 'STAFF',
      keyId: process.env.STAFF_API_KEY_ID!,
      secretKey: process.env.STAFF_SECRET_KEY!,
      permissions: ['students:read', 'students:write', 'groups:read', 'payments:read'],
      isActive: true
    };

    const studentsKey: ApiKeyConfig = {
      serverId: 'STUDENTS',
      keyId: process.env.STUDENTS_API_KEY_ID!,
      secretKey: process.env.STUDENTS_SECRET_KEY!,
      permissions: ['profile:read', 'profile:write', 'attendance:write', 'assessments:write'],
      isActive: true
    };

    this.keys.set(staffKey.keyId, staffKey);
    this.keys.set(studentsKey.keyId, studentsKey);
  }

  validateApiKey(keyId: string): ApiKeyConfig | null {
    const config = this.keys.get(keyId);
    
    if (!config || !config.isActive) {
      return null;
    }

    if (config.expiresAt && config.expiresAt < new Date()) {
      return null;
    }

    return config;
  }

  hasPermission(keyId: string, permission: string): boolean {
    const config = this.validateApiKey(keyId);
    return config ? config.permissions.includes(permission) : false;
  }

  generateSignature(
    method: string,
    path: string,
    body: string,
    timestamp: string,
    secretKey: string
  ): string {
    const payload = `${method}|${path}|${body}|${timestamp}`;
    return crypto.createHmac('sha256', secretKey).update(payload).digest('hex');
  }

  verifySignature(
    signature: string,
    method: string,
    path: string,
    body: string,
    timestamp: string,
    secretKey: string
  ): boolean {
    const expectedSignature = this.generateSignature(method, path, body, timestamp, secretKey);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }
}
```

### 2. Rate Limiting Implementation
```typescript
// lib/rate-limiter.ts
import { LRUCache } from 'lru-cache';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

export class RateLimiter {
  private cache: LRUCache<string, RateLimitEntry>;
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
    this.cache = new LRUCache({
      max: 10000,
      ttl: config.windowMs,
    });
  }

  async checkLimit(key: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    let entry = this.cache.get(key);
    
    if (!entry || entry.resetTime <= windowStart) {
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs,
      };
    }

    entry.count++;
    this.cache.set(key, entry);

    const allowed = entry.count <= this.config.maxRequests;
    const remaining = Math.max(0, this.config.maxRequests - entry.count);

    return {
      allowed,
      remaining,
      resetTime: entry.resetTime,
    };
  }
}

// Rate limit middleware
export function createRateLimitMiddleware(config: RateLimitConfig) {
  const limiter = new RateLimiter(config);

  return async (req: NextRequest) => {
    const key = req.headers.get('X-API-Key') || req.ip || 'anonymous';
    const result = await limiter.checkLimit(key);

    if (!result.allowed) {
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
            'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
          },
        }
      );
    }

    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());

    return response;
  };
}
```

## SSL/TLS Configuration

### 1. Nginx SSL Configuration
```nginx
# /etc/nginx/sites-available/staff.innovative-centre.uz
server {
    listen 443 ssl http2;
    server_name staff.innovative-centre.uz;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/staff.innovative-centre.uz.crt;
    ssl_certificate_key /etc/ssl/private/staff.innovative-centre.uz.key;
    ssl_trusted_certificate /etc/ssl/certs/ca-bundle.crt;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # IP Restrictions (VPN only)
    allow ********/24;      # VPN network
    allow ***********/24;   # Internal network
    deny all;

    # Security Headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Proxy to Staff Server
    location / {
        proxy_pass http://staff-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Block common attack patterns
    location ~* \.(php|asp|aspx|jsp)$ {
        deny all;
    }

    location ~* /\. {
        deny all;
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name staff.innovative-centre.uz;
    return 301 https://$server_name$request_uri;
}
```

### 2. Certificate Management
```bash
#!/bin/bash
# manage-certificates.sh

# Function to generate CSR
generate_csr() {
    local domain=$1
    local key_file="/etc/ssl/private/${domain}.key"
    local csr_file="/etc/ssl/csr/${domain}.csr"
    
    # Generate private key
    openssl genrsa -out "$key_file" 2048
    chmod 600 "$key_file"
    
    # Generate CSR
    openssl req -new -key "$key_file" -out "$csr_file" -subj "/C=UZ/ST=Tashkent/L=Tashkent/O=Innovative Centre/CN=${domain}"
    
    echo "CSR generated: $csr_file"
}

# Function to install certificate
install_certificate() {
    local domain=$1
    local cert_file=$2
    local ca_bundle=$3
    
    # Copy certificate files
    cp "$cert_file" "/etc/ssl/certs/${domain}.crt"
    cp "$ca_bundle" "/etc/ssl/certs/ca-bundle.crt"
    
    # Set permissions
    chmod 644 "/etc/ssl/certs/${domain}.crt"
    chmod 644 "/etc/ssl/certs/ca-bundle.crt"
    
    # Test Nginx configuration
    nginx -t && systemctl reload nginx
    
    echo "Certificate installed for $domain"
}

# Function to check certificate expiry
check_expiry() {
    local domain=$1
    local cert_file="/etc/ssl/certs/${domain}.crt"
    
    if [ -f "$cert_file" ]; then
        local expiry_date=$(openssl x509 -enddate -noout -in "$cert_file" | cut -d= -f2)
        local expiry_epoch=$(date -d "$expiry_date" +%s)
        local current_epoch=$(date +%s)
        local days_left=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        echo "Certificate for $domain expires in $days_left days"
        
        if [ $days_left -lt 30 ]; then
            echo "WARNING: Certificate expires soon!"
            # Send alert
            echo "Certificate for $domain expires in $days_left days" | mail -s "Certificate Expiry Warning" <EMAIL>
        fi
    else
        echo "Certificate file not found for $domain"
    fi
}

# Main script
case "$1" in
    generate)
        generate_csr "$2"
        ;;
    install)
        install_certificate "$2" "$3" "$4"
        ;;
    check)
        check_expiry "$2"
        ;;
    *)
        echo "Usage: $0 {generate|install|check} <domain> [cert_file] [ca_bundle]"
        exit 1
        ;;
esac
```

## Monitoring & Alerting

### 1. Security Monitoring
```typescript
// lib/security-monitor.ts
export class SecurityMonitor {
  private alertThresholds = {
    failedLogins: 5,
    unauthorizedAccess: 3,
    suspiciousActivity: 10,
  };

  async monitorFailedLogins(ip: string): Promise<void> {
    const count = await this.getFailedLoginCount(ip, 3600000); // 1 hour
    
    if (count >= this.alertThresholds.failedLogins) {
      await this.blockIP(ip, 'BRUTE_FORCE_ATTEMPT');
      await this.sendAlert('BRUTE_FORCE_DETECTED', { ip, count });
    }
  }

  async monitorUnauthorizedAccess(ip: string): Promise<void> {
    const count = await this.getUnauthorizedAccessCount(ip, 3600000);
    
    if (count >= this.alertThresholds.unauthorizedAccess) {
      await this.blockIP(ip, 'REPEATED_UNAUTHORIZED_ACCESS');
      await this.sendAlert('UNAUTHORIZED_ACCESS_PATTERN', { ip, count });
    }
  }

  private async blockIP(ip: string, reason: string): Promise<void> {
    // Add IP to firewall block list
    // Implementation depends on your firewall solution
    console.log(`Blocking IP ${ip} for reason: ${reason}`);
  }

  private async sendAlert(type: string, data: any): Promise<void> {
    // Send alert to administrators
    // Implementation depends on your alerting system
    console.log(`SECURITY ALERT: ${type}`, data);
  }
}
```

This comprehensive security implementation ensures that the staff server is properly protected with VPN-only access, while maintaining secure communication between all servers in the distributed architecture.
