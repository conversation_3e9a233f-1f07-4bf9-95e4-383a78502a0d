# Deployment & Testing Guide

## Overview
This guide provides comprehensive instructions for testing and deploying the multi-server CRM architecture, including validation procedures, monitoring setup, and rollback strategies.

## Pre-Deployment Testing

### 1. Unit Testing Strategy
```bash
# Test structure for each server
servers/
├── staff/
│   ├── __tests__/
│   │   ├── api/
│   │   │   ├── students.test.ts
│   │   │   ├── groups.test.ts
│   │   │   └── payments.test.ts
│   │   ├── middleware/
│   │   │   ├── auth.test.ts
│   │   │   └── ip-validation.test.ts
│   │   └── utils/
│   │       ├── ip-utils.test.ts
│   │       └── security.test.ts
│   └── jest.config.js
└── students/
    ├── __tests__/
    │   ├── api/
    │   │   ├── profile.test.ts
    │   │   ├── attendance.test.ts
    │   │   └── assessments.test.ts
    │   └── components/
    │       ├── dashboard.test.tsx
    │       └── profile.test.tsx
    └── jest.config.js
```

### 2. Integration Testing
```typescript
// tests/integration/inter-server-communication.test.ts
import { StaffApiClient } from '@/lib/api-clients/staff-client';
import { StudentsApiClient } from '@/lib/api-clients/students-client';

describe('Inter-Server Communication', () => {
  let staffClient: StaffApiClient;
  let studentsClient: StudentsApiClient;

  beforeAll(async () => {
    staffClient = new StaffApiClient({
      baseUrl: process.env.STAFF_SERVER_URL!,
      apiKey: process.env.STAFF_API_KEY!,
      secretKey: process.env.STAFF_SECRET_KEY!,
    });

    studentsClient = new StudentsApiClient({
      baseUrl: process.env.STUDENTS_SERVER_URL!,
      apiKey: process.env.STUDENTS_API_KEY!,
      secretKey: process.env.STUDENTS_SECRET_KEY!,
    });
  });

  describe('Student Management Flow', () => {
    it('should create student on staff server and sync to students server', async () => {
      // Create student via staff server
      const studentData = {
        name: 'Test Student',
        phone: '+998901234567',
        email: '<EMAIL>',
        level: 'A1',
        branch: 'main',
      };

      const createdStudent = await staffClient.createStudent(studentData);
      expect(createdStudent.id).toBeDefined();

      // Verify student exists in students server
      const studentProfile = await studentsClient.getProfile(createdStudent.id);
      expect(studentProfile.name).toBe(studentData.name);
      expect(studentProfile.phone).toBe(studentData.phone);
    });

    it('should handle group assignment across servers', async () => {
      const studentId = 'test-student-id';
      const groupId = 'test-group-id';

      // Assign student to group via staff server
      await staffClient.assignStudentToGroup(studentId, groupId);

      // Verify assignment in students server
      const profile = await studentsClient.getProfile(studentId);
      expect(profile.currentGroup?.id).toBe(groupId);
    });
  });

  describe('Payment Processing', () => {
    it('should record payment and sync across servers', async () => {
      const paymentData = {
        studentId: 'test-student-id',
        amount: 500000,
        method: 'CASH',
        description: 'Monthly tuition',
      };

      // Record payment via staff server
      const payment = await staffClient.recordPayment(paymentData);
      expect(payment.id).toBeDefined();

      // Verify payment appears in student profile
      const profile = await studentsClient.getProfile(paymentData.studentId);
      const studentPayment = profile.payments.find(p => p.id === payment.id);
      expect(studentPayment).toBeDefined();
      expect(studentPayment?.amount).toBe(paymentData.amount);
    });
  });
});
```

### 3. Security Testing
```typescript
// tests/security/vpn-access.test.ts
import { NextRequest } from 'next/server';
import { middleware } from '@/middleware';

describe('VPN Access Control', () => {
  it('should allow access from VPN IP range', async () => {
    const request = new NextRequest('https://staff.example.com/dashboard', {
      headers: {
        'x-forwarded-for': '*********', // VPN IP
      },
    });

    const response = await middleware(request);
    expect(response.status).not.toBe(403);
  });

  it('should deny access from non-VPN IP', async () => {
    const request = new NextRequest('https://staff.example.com/dashboard', {
      headers: {
        'x-forwarded-for': '*************', // Public IP
      },
    });

    const response = await middleware(request);
    expect(response.status).toBe(403);
  });

  it('should allow health check endpoints from any IP', async () => {
    const request = new NextRequest('https://staff.example.com/api/health', {
      headers: {
        'x-forwarded-for': '*************',
      },
    });

    const response = await middleware(request);
    expect(response.status).not.toBe(403);
  });
});

// tests/security/api-authentication.test.ts
describe('API Authentication', () => {
  it('should reject requests without API key', async () => {
    const response = await fetch('/api/inter-server/students', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name: 'Test' }),
    });

    expect(response.status).toBe(401);
  });

  it('should reject requests with invalid signature', async () => {
    const response = await fetch('/api/inter-server/students', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'valid-key',
        'X-Server-Source': 'STAFF',
        'X-Signature': 'invalid-signature',
        'X-Timestamp': new Date().toISOString(),
      },
      body: JSON.stringify({ name: 'Test' }),
    });

    expect(response.status).toBe(401);
  });
});
```

### 4. Performance Testing
```typescript
// tests/performance/load-testing.test.ts
import { performance } from 'perf_hooks';

describe('Performance Tests', () => {
  it('should handle concurrent student creation requests', async () => {
    const concurrentRequests = 50;
    const promises = [];

    const startTime = performance.now();

    for (let i = 0; i < concurrentRequests; i++) {
      promises.push(
        staffClient.createStudent({
          name: `Student ${i}`,
          phone: `+99890123456${i.toString().padStart(2, '0')}`,
          email: `student${i}@example.com`,
          level: 'A1',
          branch: 'main',
        })
      );
    }

    const results = await Promise.allSettled(promises);
    const endTime = performance.now();

    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const avgResponseTime = (endTime - startTime) / concurrentRequests;

    expect(successCount).toBeGreaterThan(concurrentRequests * 0.95); // 95% success rate
    expect(avgResponseTime).toBeLessThan(1000); // < 1 second average
  });

  it('should maintain response times under load', async () => {
    const testDuration = 30000; // 30 seconds
    const requestInterval = 100; // 100ms between requests
    const responseTimes: number[] = [];

    const startTime = Date.now();

    while (Date.now() - startTime < testDuration) {
      const requestStart = performance.now();
      
      try {
        await studentsClient.getProfile('test-student-id');
        const requestEnd = performance.now();
        responseTimes.push(requestEnd - requestStart);
      } catch (error) {
        // Log error but continue test
        console.error('Request failed:', error);
      }

      await new Promise(resolve => setTimeout(resolve, requestInterval));
    }

    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const p95ResponseTime = responseTimes.sort()[Math.floor(responseTimes.length * 0.95)];

    expect(avgResponseTime).toBeLessThan(200); // < 200ms average
    expect(p95ResponseTime).toBeLessThan(500); // < 500ms 95th percentile
  });
});
```

## Deployment Procedures

### 1. Environment Setup
```bash
#!/bin/bash
# scripts/setup-environment.sh

set -e

echo "Setting up multi-server CRM environment..."

# Create directory structure
mkdir -p servers/{staff,students,tests,ielts}
mkdir -p shared/{types,utils,api-client,security}
mkdir -p deployment/{docker,nginx,ssl}
mkdir -p monitoring/{prometheus,grafana,logs}

# Install dependencies for each server
for server in staff students; do
  echo "Setting up $server server..."
  cd servers/$server
  
  if [ ! -f package.json ]; then
    npm init -y
    npm install next react react-dom @prisma/client next-auth
    npm install -D typescript @types/node @types/react jest @types/jest
  fi
  
  cd ../..
done

# Setup shared package
cd shared
if [ ! -f package.json ]; then
  npm init -y
  npm install typescript zod
fi
cd ..

echo "Environment setup complete!"
```

### 2. Database Migration
```bash
#!/bin/bash
# scripts/migrate-databases.sh

set -e

echo "Starting database migration..."

# Backup current database
echo "Creating backup of current database..."
pg_dump $SOURCE_DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Create new databases if they don't exist
echo "Setting up staff database..."
psql $STAFF_DATABASE_URL -c "SELECT 1;" || {
  echo "Staff database not accessible. Please check connection string."
  exit 1
}

echo "Setting up students database..."
psql $STUDENTS_DATABASE_URL -c "SELECT 1;" || {
  echo "Students database not accessible. Please check connection string."
  exit 1
}

# Run migration scripts
echo "Running staff database migration..."
cd servers/staff
npx prisma db push
npx prisma generate

echo "Running students database migration..."
cd ../students
npx prisma db push
npx prisma generate

cd ../..

# Run data migration
echo "Migrating data..."
node scripts/migrate-data.js

echo "Database migration complete!"
```

### 3. Docker Deployment
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  staff-server:
    build:
      context: ./servers/staff
      dockerfile: Dockerfile.production
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${STAFF_DATABASE_URL}
      - NEXTAUTH_URL=https://staff.innovative-centre.uz
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - VPN_REQUIRED=true
    networks:
      - staff-network
      - inter-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  students-server:
    build:
      context: ./servers/students
      dockerfile: Dockerfile.production
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${STUDENTS_DATABASE_URL}
      - NEXTAUTH_URL=https://students.innovative-centre.uz
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    networks:
      - students-network
      - inter-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/ssl:/etc/ssl:ro
      - ./deployment/nginx/logs:/var/log/nginx
    depends_on:
      - staff-server
      - students-server
    restart: unless-stopped
    networks:
      - staff-network
      - students-network

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - monitoring

networks:
  staff-network:
    driver: bridge
  students-network:
    driver: bridge
  inter-server:
    driver: bridge
  monitoring:
    driver: bridge

volumes:
  prometheus-data:
  grafana-data:
```

### 4. Deployment Script
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-production}
VERSION=${2:-latest}

echo "Deploying CRM multi-server architecture..."
echo "Environment: $ENVIRONMENT"
echo "Version: $VERSION"

# Pre-deployment checks
echo "Running pre-deployment checks..."
./scripts/pre-deployment-checks.sh

# Build and tag images
echo "Building Docker images..."
docker-compose -f docker-compose.production.yml build

# Tag images with version
docker tag inno-crm_staff-server:latest inno-crm_staff-server:$VERSION
docker tag inno-crm_students-server:latest inno-crm_students-server:$VERSION

# Run database migrations
echo "Running database migrations..."
./scripts/migrate-databases.sh

# Deploy services
echo "Deploying services..."
docker-compose -f docker-compose.production.yml up -d

# Wait for services to be healthy
echo "Waiting for services to be healthy..."
./scripts/wait-for-health.sh

# Run post-deployment tests
echo "Running post-deployment tests..."
./scripts/post-deployment-tests.sh

# Update monitoring
echo "Updating monitoring configuration..."
./scripts/update-monitoring.sh

echo "Deployment completed successfully!"
echo "Staff Server: https://staff.innovative-centre.uz"
echo "Students Server: https://students.innovative-centre.uz"
echo "Monitoring: https://monitoring.innovative-centre.uz"
```

### 5. Health Check Script
```bash
#!/bin/bash
# scripts/wait-for-health.sh

set -e

TIMEOUT=300 # 5 minutes
INTERVAL=10 # 10 seconds

check_service() {
  local service_name=$1
  local health_url=$2
  
  echo "Checking health of $service_name..."
  
  for ((i=0; i<$((TIMEOUT/INTERVAL)); i++)); do
    if curl -f -s "$health_url" > /dev/null; then
      echo "$service_name is healthy"
      return 0
    fi
    
    echo "Waiting for $service_name to be healthy... ($((i*INTERVAL))s)"
    sleep $INTERVAL
  done
  
  echo "ERROR: $service_name failed to become healthy within $TIMEOUT seconds"
  return 1
}

# Check all services
check_service "Staff Server" "https://staff.innovative-centre.uz/api/health"
check_service "Students Server" "https://students.innovative-centre.uz/api/health"

echo "All services are healthy!"
```

## Monitoring Setup

### 1. Prometheus Configuration
```yaml
# monitoring/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'staff-server'
    static_configs:
      - targets: ['staff-server:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'students-server'
    static_configs:
      - targets: ['students-server:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
```

### 2. Grafana Dashboards
```json
{
  "dashboard": {
    "title": "CRM Multi-Server Overview",
    "panels": [
      {
        "title": "Server Response Times",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Inter-Server API Calls",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(inter_server_requests_total[5m])",
            "legendFormat": "{{source_server}} -> {{target_server}}"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "singlestat",
        "targets": [
          {
            "expr": "sum(database_connections_active)",
            "legendFormat": "Active Connections"
          }
        ]
      }
    ]
  }
}
```

## Rollback Procedures

### 1. Rollback Script
```bash
#!/bin/bash
# scripts/rollback.sh

set -e

PREVIOUS_VERSION=${1:-previous}

echo "Rolling back to version: $PREVIOUS_VERSION"

# Stop current services
echo "Stopping current services..."
docker-compose -f docker-compose.production.yml down

# Restore previous images
echo "Restoring previous images..."
docker tag inno-crm_staff-server:$PREVIOUS_VERSION inno-crm_staff-server:latest
docker tag inno-crm_students-server:$PREVIOUS_VERSION inno-crm_students-server:latest

# Restore database if needed
if [ "$2" = "--restore-db" ]; then
  echo "Restoring database..."
  ./scripts/restore-database.sh $PREVIOUS_VERSION
fi

# Start services with previous version
echo "Starting services with previous version..."
docker-compose -f docker-compose.production.yml up -d

# Wait for health
./scripts/wait-for-health.sh

echo "Rollback completed successfully!"
```

This comprehensive testing and deployment guide ensures a smooth transition to the multi-server architecture while maintaining system reliability and performance.
