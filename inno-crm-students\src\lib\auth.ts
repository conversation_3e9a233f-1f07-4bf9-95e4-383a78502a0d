// Simple auth configuration to avoid type conflicts
export const authOptions = {
  providers: [
    {
      id: 'credentials',
      name: 'credentials',
      type: 'credentials',
      credentials: {
        phone: { label: 'Phone', type: 'text' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials: any) {
        if (!credentials?.phone || !credentials?.password) {
          return null
        }

        try {
          const { db } = await import('./database')
          const bcrypt = await import('bcryptjs')

          const user = await db.user.findUnique({
            where: { phone: credentials.phone }
          })

          if (!user) {
            return null
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          )

          if (!isPasswordValid) {
            return null
          }

          return {
            id: user.id,
            name: user.name,
            phone: user.phone,
            email: user.email || '',
            role: user.role,
          }
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    }
  ],
  session: {
    strategy: 'jwt' as const
  },
  callbacks: {
    jwt: async ({ token, user }: any) => {
      if (user) {
        token.role = user.role
      }
      return token
    },
    session: async ({ session, token }: any) => {
      if (token) {
        session.user.id = token.sub
        session.user.role = token.role
      }
      return session
    }
  },
  pages: {
    signIn: '/auth/signin'
  }
}
