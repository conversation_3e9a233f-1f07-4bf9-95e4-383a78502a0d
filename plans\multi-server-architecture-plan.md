# Multi-Server CRM Architecture Plan

## Overview
This document outlines the comprehensive plan for scaling the existing monolithic CRM system into multiple specialized servers with enhanced security, performance, and maintainability.

## Current State Analysis
- **Monolithic Architecture**: Single Next.js application handling all functionality
- **Single Database**: PostgreSQL database containing all entities
- **Mixed User Roles**: Staff and students using the same interface
- **Security Concerns**: No IP-based access control for sensitive operations

## Target Architecture

### Server Structure
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Staff Server  │    │ Students Server │    │  Tests Server   │    │  IELTS Server   │
│   (VPN Only)    │    │   (Public)      │    │   (Future)      │    │   (Future)      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Admin Panel   │    │ • Student Portal│    │ • Test Engine   │    │ • IELTS Tests   │
│ • User Mgmt     │    │ • Assignments   │    │ • Question Bank │    │ • Score Reports │
│ • Financial     │    │ • Progress      │    │ • Analytics     │    │ • Certificates  │
│ • Reports       │    │ • Payments      │    │ • Scheduling    │    │ • Prep Materials│
│ • Teacher Mgmt  │    │ • Messages      │    │ • Results       │    │ • Mock Tests    │
│ • Groups        │    │ • Attendance    │    │ • Proctoring    │    │ • Band Calc     │
│ • Leads         │    │ • Resources     │    │ • Integrity     │    │ • Speaking Rec  │
│ • Analytics     │    │ • Notifications │    │ • Reporting     │    │ • Writing Eval  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         └───────────────────────┼───────────────────────┼───────────────────────┘
                                 │                       │
                    ┌─────────────────────────────────────┐
                    │     Inter-Server Communication      │
                    │         (Secure REST APIs)          │
                    └─────────────────────────────────────┘
```

### Database Architecture
```
┌─────────────────────┐              ┌─────────────────────┐
│   Staff Database    │              │  Students Database  │
│  (crm-staff)        │              │  (crm-students)     │
├─────────────────────┤              ├─────────────────────┤
│ • Users (Staff)     │◄────────────►│ • Users (Students)  │
│ • Teachers          │              │ • Student Profiles  │
│ • Groups            │              │ • Enrollments       │
│ • Courses           │              │ • Payments          │
│ • Leads             │              │ • Attendance        │
│ • Cabinets          │              │ • Assessments       │
│ • Activity Logs     │              │ • Messages          │
│ • Reports           │              │ • Notifications     │
│ • Financial Data    │              │ • Progress Data     │
└─────────────────────┘              └─────────────────────┘
```

## Server Responsibilities

### 1. Staff Server (VPN-Protected)
**Access Control**: IP-based restriction via VPN
**Database**: `crm-staff` (PostgreSQL)
**Port**: 3001
**Domain**: `staff.innovative-centre.uz`

**Core Features**:
- Administrative dashboard
- User management (all roles)
- Teacher management and KPIs
- Group creation and management
- Lead management and call center
- Financial operations and reporting
- Student enrollment and transfers
- System analytics and reports
- Activity logging and monitoring
- Cabinet and schedule management

**Security Features**:
- VPN-only access
- IP whitelist validation
- Enhanced authentication
- Audit logging
- Session management

### 2. Students Server (Public Access)
**Access Control**: Standard authentication
**Database**: `crm-students` (PostgreSQL)
**Port**: 3002
**Domain**: `students.innovative-centre.uz`

**Core Features**:
- Student portal and dashboard
- Assignment viewing and submission
- Progress tracking and reports
- Payment history and status
- Attendance records
- Message center
- Resource downloads
- Schedule viewing
- Notification center
- Profile management

**Security Features**:
- Standard authentication
- Rate limiting
- Input validation
- CSRF protection
- Session security

### 3. Tests Server (Future)
**Access Control**: Role-based (Teachers, Students)
**Database**: `crm-tests` (PostgreSQL)
**Port**: 3003
**Domain**: `tests.innovative-centre.uz`

**Planned Features**:
- Test creation and management
- Question bank management
- Test scheduling and assignment
- Automated grading
- Result analytics
- Proctoring integration
- Academic integrity monitoring

### 4. IELTS Server (Future)
**Access Control**: Role-based (IELTS Staff, Students)
**Database**: `crm-ielts` (PostgreSQL)
**Port**: 3004
**Domain**: `ielts.innovative-centre.uz`

**Planned Features**:
- IELTS-specific test engine
- Speaking test recording
- Writing evaluation tools
- Band score calculation
- Certificate generation
- Preparation materials
- Mock test scheduling

## Inter-Server Communication

### API Architecture
```
┌─────────────────┐    HTTP/HTTPS     ┌─────────────────┐
│   Staff Server  │◄─────────────────►│ Students Server │
│                 │   Secure APIs     │                 │
│ • Create Student│                   │ • Get Profile   │
│ • Update Groups │                   │ • Submit Payment│
│ • Send Messages │                   │ • View Schedule │
│ • Track Progress│                   │ • Get Messages  │
└─────────────────┘                   └─────────────────┘
```

### Communication Protocols
1. **Authentication**: JWT tokens with server-specific secrets
2. **Data Format**: JSON with standardized schemas
3. **Error Handling**: Consistent error codes and messages
4. **Rate Limiting**: Per-server API quotas
5. **Logging**: Comprehensive request/response logging

### API Endpoints Structure
```
Staff Server APIs:
POST /api/inter-server/students/create
PUT  /api/inter-server/students/{id}
GET  /api/inter-server/students/{id}
POST /api/inter-server/groups/assign-student
POST /api/inter-server/messages/send

Students Server APIs:
GET  /api/inter-server/profile/{studentId}
POST /api/inter-server/payments/record
GET  /api/inter-server/schedule/{studentId}
POST /api/inter-server/attendance/mark
```

## Security Implementation

### VPN Configuration for Staff Server
1. **VPN Server Setup**: Dedicated VPN server for staff access
2. **IP Whitelist**: Middleware to validate incoming IP addresses
3. **Fail2Ban**: Automatic blocking of suspicious access attempts
4. **SSL/TLS**: End-to-end encryption for all communications

### Inter-Server Security
1. **API Keys**: Unique keys for each server pair
2. **Request Signing**: HMAC signatures for request validation
3. **Rate Limiting**: Prevent abuse and DoS attacks
4. **Audit Logging**: Track all inter-server communications

## Data Synchronization Strategy

### Real-time Sync Requirements
- Student enrollment changes
- Payment updates
- Group assignments
- Message delivery
- Schedule updates

### Sync Mechanisms
1. **Event-Driven**: Webhook notifications for critical updates
2. **Polling**: Regular sync for non-critical data
3. **Conflict Resolution**: Last-write-wins with timestamp validation
4. **Backup Sync**: Daily full synchronization for data integrity

## Deployment Strategy

### Infrastructure Requirements
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   VPN Gateway   │    │  Database Cluster│
│   (Nginx/HAProxy)│    │   (OpenVPN)     │    │   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    Docker Swarm / Kubernetes                    │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  Staff Server   │ Students Server │  Tests Server   │IELTS Server│
│   Container     │   Container     │   Container     │ Container │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

### Deployment Steps
1. **Database Migration**: Separate existing data into staff/student schemas
2. **Server Deployment**: Deploy staff and students servers
3. **VPN Setup**: Configure VPN access for staff server
4. **API Integration**: Implement inter-server communication
5. **Testing**: Comprehensive testing of all functionalities
6. **Monitoring**: Set up logging and monitoring systems

## Monitoring and Maintenance

### Health Checks
- Server availability monitoring
- Database connection health
- API response times
- Inter-server communication status

### Logging Strategy
- Centralized logging with ELK stack
- Structured logging with correlation IDs
- Performance metrics collection
- Security event monitoring

### Backup Strategy
- Automated daily backups for each database
- Cross-server backup verification
- Point-in-time recovery capabilities
- Disaster recovery procedures

## Migration Timeline

### Phase 1: Foundation (Weeks 1-2)
- Database schema separation
- Basic server setup
- Core API development

### Phase 2: Core Features (Weeks 3-4)
- Staff server implementation
- Students server implementation
- Basic inter-server communication

### Phase 3: Security & Testing (Weeks 5-6)
- VPN integration
- Security hardening
- Comprehensive testing

### Phase 4: Deployment & Monitoring (Weeks 7-8)
- Production deployment
- Monitoring setup
- Performance optimization

### Phase 5: Future Servers (Weeks 9-12)
- Tests server development
- IELTS server development
- Full system integration

## Success Metrics
- **Performance**: <200ms API response times
- **Availability**: 99.9% uptime for each server
- **Security**: Zero unauthorized access incidents
- **Scalability**: Support for 10x current user load
- **Maintainability**: <2 hours for feature deployments

## Risk Mitigation
- **Data Loss**: Automated backups and replication
- **Security Breach**: Multi-layer security and monitoring
- **Server Failure**: Load balancing and failover mechanisms
- **Communication Failure**: Retry mechanisms and circuit breakers
- **Performance Issues**: Caching and optimization strategies
