# Multi-Server CRM Implementation Guide

## Step-by-Step Implementation Plan

### Prerequisites
- Node.js 18+ installed
- Docker and Docker Compose
- PostgreSQL databases (Staff & Students)
- VPN server access
- SSL certificates for domains

## Phase 1: Database Schema Separation

### 1.1 Analyze Current Schema Dependencies
```bash
# Run dependency analysis
node scripts/analyze-schema-dependencies.js
```

### 1.2 Create Staff Database Schema
```sql
-- Staff Database (crm-staff)
-- Contains: Users (staff), Teachers, Groups, Courses, Leads, Cabinets, Activity Logs, Financial Data

-- Core staff entities
CREATE TABLE users (staff only);
CREATE TABLE teachers;
CREATE TABLE groups;
CREATE TABLE courses;
CREATE TABLE leads;
CREATE TABLE cabinets;
CREATE TABLE activity_logs;
CREATE TABLE payments (admin view);

-- Reference tables for students (IDs only)
CREATE TABLE student_references (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  phone VARCHAR NOT NULL,
  current_group_id VARCHAR,
  status VARCHAR NOT NULL,
  branch VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 1.3 Create Students Database Schema
```sql
-- Students Database (crm-students)
-- Contains: Users (students), Student Profiles, Enrollments, Payments, Attendance, Assessments

-- Core student entities
CREATE TABLE users (students only);
CREATE TABLE students;
CREATE TABLE enrollments;
CREATE TABLE payments (student view);
CREATE TABLE attendance;
CREATE TABLE assessments;
CREATE TABLE messages;
CREATE TABLE notifications;

-- Reference tables for staff entities (IDs only)
CREATE TABLE group_references (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  teacher_name VARCHAR NOT NULL,
  schedule VARCHAR NOT NULL,
  room VARCHAR,
  branch VARCHAR NOT NULL
);

CREATE TABLE teacher_references (
  id VARCHAR PRIMARY KEY,
  name VARCHAR NOT NULL,
  subject VARCHAR NOT NULL,
  branch VARCHAR NOT NULL
);
```

### 1.4 Data Migration Scripts
```javascript
// scripts/migrate-to-multi-server.js
const { PrismaClient } = require('@prisma/client');

async function migrateData() {
  const sourceDb = new PrismaClient({
    datasourceUrl: process.env.SOURCE_DATABASE_URL
  });
  
  const staffDb = new PrismaClient({
    datasourceUrl: process.env.STAFF_DATABASE_URL
  });
  
  const studentsDb = new PrismaClient({
    datasourceUrl: process.env.STUDENTS_DATABASE_URL
  });

  // Migrate staff data
  await migrateStaffData(sourceDb, staffDb);
  
  // Migrate student data
  await migrateStudentData(sourceDb, studentsDb);
  
  // Create reference tables
  await createReferenceTables(staffDb, studentsDb);
}
```

## Phase 2: Server Structure Setup

### 2.1 Project Structure
```
inno-crm/
├── servers/
│   ├── staff/                 # Staff server
│   │   ├── app/
│   │   ├── components/
│   │   ├── lib/
│   │   ├── prisma/
│   │   ├── package.json
│   │   └── next.config.js
│   ├── students/              # Students server
│   │   ├── app/
│   │   ├── components/
│   │   ├── lib/
│   │   ├── prisma/
│   │   ├── package.json
│   │   └── next.config.js
│   ├── tests/                 # Future tests server
│   └── ielts/                 # Future IELTS server
├── shared/                    # Shared utilities
│   ├── types/
│   ├── utils/
│   ├── api-client/
│   └── security/
├── docker-compose.yml
├── nginx.conf
└── deployment/
```

### 2.2 Shared Components Setup
```bash
# Create shared package
mkdir shared
cd shared
npm init -y
npm install typescript zod

# Create shared types
mkdir types
touch types/index.ts
touch types/api.ts
touch types/database.ts
```

### 2.3 Docker Configuration
```yaml
# docker-compose.yml
version: '3.8'
services:
  staff-server:
    build: ./servers/staff
    ports:
      - "3001:3000"
    environment:
      - DATABASE_URL=${STAFF_DATABASE_URL}
      - NEXTAUTH_URL=https://staff.innovative-centre.uz
      - VPN_REQUIRED=true
    networks:
      - staff-network
      - inter-server

  students-server:
    build: ./servers/students
    ports:
      - "3002:3000"
    environment:
      - DATABASE_URL=${STUDENTS_DATABASE_URL}
      - NEXTAUTH_URL=https://students.innovative-centre.uz
    networks:
      - students-network
      - inter-server

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
    depends_on:
      - staff-server
      - students-server

networks:
  staff-network:
  students-network:
  inter-server:
```

## Phase 3: Staff Server Implementation

### 3.1 Staff Server Setup
```bash
# Create staff server
mkdir servers/staff
cd servers/staff
npm init -y
npm install next react react-dom @prisma/client next-auth
npm install -D typescript @types/node @types/react
```

### 3.2 Staff Server Configuration
```javascript
// servers/staff/next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    SERVER_TYPE: 'STAFF',
    VPN_REQUIRED: 'true',
  },
}

module.exports = nextConfig
```

### 3.3 VPN Middleware Implementation
```typescript
// servers/staff/middleware.ts
import { NextRequest, NextResponse } from 'next/server';

const ALLOWED_VPN_IPS = [
  '10.0.0.0/8',     // VPN network range
  '***********/24', // Local network
  '127.0.0.1',      // Localhost for development
];

export function middleware(request: NextRequest) {
  const clientIP = request.ip || 
    request.headers.get('x-forwarded-for') || 
    request.headers.get('x-real-ip');

  // Skip IP check in development
  if (process.env.NODE_ENV === 'development') {
    return NextResponse.next();
  }

  if (!isIPAllowed(clientIP)) {
    return new NextResponse('Access Denied: VPN Required', { 
      status: 403 
    });
  }

  return NextResponse.next();
}

function isIPAllowed(ip: string): boolean {
  // Implement IP range checking logic
  return ALLOWED_VPN_IPS.some(range => isIPInRange(ip, range));
}

export const config = {
  matcher: [
    '/((?!api/health|_next/static|_next/image|favicon.ico).*)',
  ],
}
```

### 3.4 Staff API Routes
```typescript
// servers/staff/app/api/students/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { staffDb } from '@/lib/database';
import { studentsApiClient } from '@/lib/api-clients';

export async function POST(request: NextRequest) {
  const session = await getServerSession();
  
  if (!session || !['ADMIN', 'MANAGER'].includes(session.user.role)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const studentData = await request.json();
  
  try {
    // Create student in students server
    const student = await studentsApiClient.createStudent(studentData);
    
    // Create reference in staff database
    await staffDb.studentReference.create({
      data: {
        id: student.id,
        name: student.name,
        phone: student.phone,
        status: student.status,
        branch: student.branch,
      }
    });

    return NextResponse.json(student);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create student' }, { status: 500 });
  }
}
```

## Phase 4: Students Server Implementation

### 4.1 Students Server Setup
```bash
# Create students server
mkdir servers/students
cd servers/students
npm init -y
npm install next react react-dom @prisma/client next-auth
npm install -D typescript @types/node @types/react
```

### 4.2 Student Portal Features
```typescript
// servers/students/app/dashboard/page.tsx
import { getServerSession } from 'next-auth';
import { StudentDashboard } from '@/components/dashboard/StudentDashboard';
import { studentsDb } from '@/lib/database';

export default async function DashboardPage() {
  const session = await getServerSession();
  
  if (!session || session.user.role !== 'STUDENT') {
    redirect('/auth/signin');
  }

  const student = await studentsDb.student.findUnique({
    where: { userId: session.user.id },
    include: {
      currentGroup: true,
      payments: true,
      attendances: true,
      assessments: true,
    }
  });

  return <StudentDashboard student={student} />;
}
```

### 4.3 Students API Routes
```typescript
// servers/students/app/api/profile/route.ts
export async function GET(request: NextRequest) {
  const session = await getServerSession();
  
  if (!session || session.user.role !== 'STUDENT') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const student = await studentsDb.student.findUnique({
    where: { userId: session.user.id },
    include: {
      currentGroup: true,
      payments: { orderBy: { createdAt: 'desc' } },
      attendances: { orderBy: { createdAt: 'desc' } },
    }
  });

  return NextResponse.json(student);
}
```

## Phase 5: Inter-Server Communication

### 5.1 API Client Implementation
```typescript
// shared/api-client/students-client.ts
export class StudentsApiClient {
  private baseUrl: string;
  private apiKey: string;

  constructor(baseUrl: string, apiKey: string) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
  }

  async createStudent(data: CreateStudentData): Promise<Student> {
    const response = await fetch(`${this.baseUrl}/api/inter-server/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Server-Source': 'STAFF',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create student: ${response.statusText}`);
    }

    return response.json();
  }

  async updateStudent(id: string, data: UpdateStudentData): Promise<Student> {
    const response = await fetch(`${this.baseUrl}/api/inter-server/students/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Server-Source': 'STAFF',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update student: ${response.statusText}`);
    }

    return response.json();
  }
}
```

### 5.2 Authentication Middleware
```typescript
// shared/middleware/inter-server-auth.ts
export function createInterServerAuthMiddleware(validApiKeys: string[]) {
  return (req: NextRequest, res: NextResponse, next: NextFunction) => {
    const apiKey = req.headers.get('X-API-Key');
    const serverSource = req.headers.get('X-Server-Source');

    if (!apiKey || !validApiKeys.includes(apiKey)) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 });
    }

    if (!serverSource || !['STAFF', 'STUDENTS', 'TESTS', 'IELTS'].includes(serverSource)) {
      return NextResponse.json({ error: 'Invalid server source' }, { status: 400 });
    }

    // Add server info to request
    req.serverSource = serverSource;
    return next();
  };
}
```

## Phase 6: Security Implementation

### 6.1 VPN Server Configuration
```bash
# Install OpenVPN
sudo apt update
sudo apt install openvpn easy-rsa

# Configure VPN server
sudo make-cadir /etc/openvpn/easy-rsa
cd /etc/openvpn/easy-rsa
sudo ./easyrsa init-pki
sudo ./easyrsa build-ca
sudo ./easyrsa gen-req server nopass
sudo ./easyrsa sign-req server server
```

### 6.2 Nginx Configuration
```nginx
# nginx.conf
upstream staff_backend {
    server staff-server:3000;
}

upstream students_backend {
    server students-server:3000;
}

server {
    listen 443 ssl;
    server_name staff.innovative-centre.uz;
    
    ssl_certificate /etc/ssl/staff.crt;
    ssl_certificate_key /etc/ssl/staff.key;
    
    # VPN IP restriction
    allow 10.0.0.0/8;
    allow ***********/24;
    deny all;
    
    location / {
        proxy_pass http://staff_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}

server {
    listen 443 ssl;
    server_name students.innovative-centre.uz;
    
    ssl_certificate /etc/ssl/students.crt;
    ssl_certificate_key /etc/ssl/students.key;
    
    location / {
        proxy_pass http://students_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## Phase 7: Testing & Deployment

### 7.1 Testing Strategy
```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e

# Security tests
npm run test:security

# Performance tests
npm run test:performance
```

### 7.2 Deployment Scripts
```bash
#!/bin/bash
# deploy.sh

# Build all servers
docker-compose build

# Run database migrations
docker-compose run staff-server npm run db:migrate
docker-compose run students-server npm run db:migrate

# Start services
docker-compose up -d

# Health checks
./scripts/health-check.sh

echo "Deployment completed successfully!"
```

## Phase 8: Monitoring & Maintenance

### 8.1 Health Check Endpoints
```typescript
// Health check implementation for each server
export async function GET() {
  const checks = {
    database: await checkDatabase(),
    interServerComm: await checkInterServerCommunication(),
    vpnAccess: await checkVPNAccess(), // Staff server only
  };

  const isHealthy = Object.values(checks).every(check => check.status === 'ok');

  return NextResponse.json({
    status: isHealthy ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString(),
  }, {
    status: isHealthy ? 200 : 503
  });
}
```

### 8.2 Monitoring Setup
```yaml
# monitoring/docker-compose.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node

  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
```

This implementation guide provides a comprehensive roadmap for transforming the monolithic CRM into a secure, scalable multi-server architecture. Each phase builds upon the previous one, ensuring a smooth transition while maintaining system functionality and security.
