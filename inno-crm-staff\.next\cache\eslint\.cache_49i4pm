[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\layout\\sidebar.tsx": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dashboard-card.tsx": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth.ts": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database.ts": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils.ts": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\layout\\sidebar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\ui\\dashboard-card.tsx": "13", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\auth.ts": "14", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\database.ts": "15", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\utils.ts": "16", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\types\\next-auth.d.ts": "17"}, {"size": 1120, "mtime": 1750433989876, "results": "18", "hashOfConfig": "19"}, {"size": 2426, "mtime": 1750433980033, "results": "20", "hashOfConfig": "19"}, {"size": 1948, "mtime": 1750433966391, "results": "21", "hashOfConfig": "19"}, {"size": 1037, "mtime": 1750433955183, "results": "22", "hashOfConfig": "19"}, {"size": 1579, "mtime": 1750433069293, "results": "23", "hashOfConfig": "19"}, {"size": 271, "mtime": 1750433059805, "results": "24", "hashOfConfig": "19"}, {"size": 166, "mtime": 1750433075388, "results": "25", "hashOfConfig": "19"}, {"size": 1120, "mtime": 1750435130914, "results": "26", "hashOfConfig": "19"}, {"size": 2426, "mtime": 1750435121562, "results": "27", "hashOfConfig": "19"}, {"size": 689, "mtime": 1750361517867, "results": "28", "hashOfConfig": "19"}, {"size": 104, "mtime": 1750434075299, "results": "29", "hashOfConfig": "19"}, {"size": 1948, "mtime": 1750435080971, "results": "30", "hashOfConfig": "19"}, {"size": 1037, "mtime": 1750435067552, "results": "31", "hashOfConfig": "19"}, {"size": 1592, "mtime": 1750435288402, "results": "32", "hashOfConfig": "19"}, {"size": 271, "mtime": 1750435089661, "results": "33", "hashOfConfig": "19"}, {"size": 166, "mtime": 1750435106432, "results": "34", "hashOfConfig": "19"}, {"size": 390, "mtime": 1750435139807, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8ci4t3", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\layout\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-staff\\src\\types\\next-auth.d.ts", ["87"], [], {"ruleId": "88", "severity": 2, "message": "89", "line": 1, "column": 8, "nodeType": null, "messageId": "90", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'NextAuth' is defined but never used.", "unusedVar"]